#!/usr/bin/env python3
"""
项目结构检查脚本
检查项目文件是否完整
"""
import os
import sys
from pathlib import Path

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {file_path} - {description}")
        return True
    else:
        print(f"❌ {file_path} - {description} (缺失)")
        return False

def check_directory_exists(dir_path, description=""):
    """检查目录是否存在"""
    if os.path.isdir(dir_path):
        print(f"✅ {dir_path}/ - {description}")
        return True
    else:
        print(f"❌ {dir_path}/ - {description} (缺失)")
        return False

def main():
    """主检查函数"""
    print("🔍 检查项目结构...")
    print("=" * 50)
    
    # 项目根目录
    root_dir = Path(__file__).parent.parent
    os.chdir(root_dir)
    
    missing_count = 0
    
    # 检查根目录文件
    files_to_check = [
        ("README.md", "项目说明文档"),
        (".gitignore", "Git 忽略文件"),
        ("startup.sh", "项目启动脚本"),
    ]
    
    print("\n📁 根目录文件:")
    for file_path, desc in files_to_check:
        if not check_file_exists(file_path, desc):
            missing_count += 1
    
    # 检查目录结构
    directories_to_check = [
        ("backend", "后端目录"),
        ("frontend", "前端目录"),
        ("docs", "文档目录"),
        ("scripts", "脚本目录"),
    ]
    
    print("\n📁 主要目录:")
    for dir_path, desc in directories_to_check:
        if not check_directory_exists(dir_path, desc):
            missing_count += 1
    
    # 检查后端文件
    backend_files = [
        ("backend/requirements.txt", "Python 依赖文件"),
        ("backend/app/main.py", "FastAPI 主应用"),
        ("backend/app/database.py", "数据库配置"),
        ("backend/config/settings.py", "应用配置"),
        ("backend/agents/financial_agent.py", "财务 Agent"),
        ("backend/agents/audit_agent.py", "审计 Agent"),
        ("backend/langgraph/workflow.py", "LangGraph 工作流"),
        ("backend/services/memory_service.py", "记忆服务"),
        ("backend/services/conversation_service.py", "对话服务"),
        ("backend/services/langgraph_service.py", "LangGraph 服务"),
        ("backend/utils/accounting_tools.py", "会计工具"),
        ("backend/models/schemas.py", "数据模型"),
    ]
    
    print("\n🐍 后端文件:")
    for file_path, desc in backend_files:
        if not check_file_exists(file_path, desc):
            missing_count += 1
    
    # 检查前端文件
    frontend_files = [
        ("frontend/package.json", "Node.js 依赖文件"),
        ("frontend/public/index.html", "HTML 模板"),
        ("frontend/src/index.tsx", "React 入口文件"),
        ("frontend/src/App.tsx", "主应用组件"),
        ("frontend/src/types/index.ts", "TypeScript 类型定义"),
        ("frontend/src/stores/conversationStore.ts", "对话状态管理"),
        ("frontend/src/stores/workflowStore.ts", "工作流状态管理"),
        ("frontend/src/services/conversationService.ts", "对话服务"),
        ("frontend/src/components/Layout/Layout.tsx", "布局组件"),
        ("frontend/src/pages/Dashboard/Dashboard.tsx", "仪表板页面"),
        ("frontend/src/pages/Conversation/ConversationPage.tsx", "对话页面"),
    ]
    
    print("\n⚛️ 前端文件:")
    for file_path, desc in frontend_files:
        if not check_file_exists(file_path, desc):
            missing_count += 1
    
    # 检查 API 路由
    api_files = [
        ("backend/app/api/__init__.py", "API 路由初始化"),
        ("backend/app/api/conversations.py", "对话 API"),
        ("backend/app/api/agents.py", "Agent API"),
        ("backend/app/api/vouchers.py", "凭证 API"),
        ("backend/app/api/files.py", "文件 API"),
    ]
    
    print("\n🌐 API 路由:")
    for file_path, desc in api_files:
        if not check_file_exists(file_path, desc):
            missing_count += 1
    
    # 检查文档
    doc_files = [
        ("docs/PROJECT_SUMMARY.md", "项目总结文档"),
    ]
    
    print("\n📚 文档文件:")
    for file_path, desc in doc_files:
        if not check_file_exists(file_path, desc):
            missing_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    if missing_count == 0:
        print("🎉 项目结构检查完成！所有文件都存在。")
        print("✅ 项目已准备就绪，可以运行 ./startup.sh setup 进行初始化")
    else:
        print(f"⚠️  项目结构检查完成，发现 {missing_count} 个缺失文件。")
        print("❌ 请检查并补充缺失的文件")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
