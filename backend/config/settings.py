"""
应用配置设置
"""
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用设置"""
    
    # 应用基础配置
    app_name: str = "财务会计记账凭证AI Agent"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    database_url: str = "sqlite:///./accounting_agent.db"
    redis_url: str = "redis://localhost:6379/0"
    
    # AI 模型配置
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # 默认模型设置
    default_llm_provider: str = "openai"
    default_model: str = "gpt-4-turbo-preview"
    
    # LangGraph 配置
    langgraph_checkpoint_dir: str = "./langgraph_checkpoints"
    
    # 内存配置
    memory_backend: str = "redis"  # redis, sqlite, memory
    
    # 文件上传配置
    upload_dir: str = "./uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_file_types: List[str] = [".pdf", ".docx", ".xlsx", ".csv", ".txt"]
    
    # 安全配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS 配置
    cors_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
    ]
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局设置实例
settings = Settings()
