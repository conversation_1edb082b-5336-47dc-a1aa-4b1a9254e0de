# FastAPI and web framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# <PERSON><PERSON><PERSON><PERSON> and LangGraph
langchain>=0.1.0
langchain-community>=0.0.10
langchain-core>=0.1.0
langchain-openai>=0.0.2
langgraph>=0.0.20
langsmith>=0.0.69

# Database
sqlalchemy>=2.0.23
alembic>=1.13.1
psycopg2-binary>=2.9.9
redis>=5.0.1

# Pydantic for data validation
pydantic>=2.5.0
pydantic-settings>=2.1.0

# HTTP client
httpx>=0.25.2
aiohttp>=3.9.1

# Utilities
python-dotenv>=1.0.0
loguru>=0.7.2
typer>=0.9.0
rich>=13.7.0

# AI/ML libraries
openai>=1.3.7
anthropic>=0.7.8
tiktoken>=0.5.2
numpy>=1.24.4
pandas>=2.1.4

# Memory and storage
chromadb>=0.4.18
faiss-cpu>=1.7.4

# File processing
pypdf>=3.17.4
python-docx>=1.1.0
openpyxl>=3.1.2

# Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
httpx>=0.25.2

# Development
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1
