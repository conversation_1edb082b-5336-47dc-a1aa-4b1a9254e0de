"""
Pydantic 数据模型定义
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class AgentType(str, Enum):
    """Agent 类型"""
    FINANCIAL = "financial"
    AUDIT = "audit"


class MessageRole(str, Enum):
    """消息角色"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ConversationStatus(str, Enum):
    """对话状态"""
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    ERROR = "error"


class VoucherStatus(str, Enum):
    """凭证状态"""
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    REJECTED = "rejected"


# 基础模型
class BaseSchema(BaseModel):
    """基础模型"""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


# 消息相关模型
class MessageCreate(BaseModel):
    """创建消息"""
    content: str = Field(..., description="消息内容")
    role: MessageRole = Field(..., description="消息角色")
    agent_type: Optional[AgentType] = Field(None, description="Agent类型")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")


class MessageResponse(BaseSchema):
    """消息响应"""
    id: str
    content: str
    role: MessageRole
    agent_type: Optional[AgentType]
    conversation_id: str
    metadata: Dict[str, Any]


# 对话相关模型
class ConversationCreate(BaseModel):
    """创建对话"""
    title: Optional[str] = Field(None, description="对话标题")
    description: Optional[str] = Field(None, description="对话描述")


class ConversationResponse(BaseSchema):
    """对话响应"""
    id: str
    title: Optional[str]
    description: Optional[str]
    status: ConversationStatus
    messages: List[MessageResponse] = []


# 记账凭证相关模型
class AccountingEntry(BaseModel):
    """会计分录"""
    account_code: str = Field(..., description="科目代码")
    account_name: str = Field(..., description="科目名称")
    debit_amount: Optional[float] = Field(None, description="借方金额")
    credit_amount: Optional[float] = Field(None, description="贷方金额")
    description: Optional[str] = Field(None, description="摘要")


class VoucherCreate(BaseModel):
    """创建凭证"""
    voucher_date: datetime = Field(..., description="凭证日期")
    voucher_number: Optional[str] = Field(None, description="凭证号")
    description: str = Field(..., description="凭证摘要")
    entries: List[AccountingEntry] = Field(..., description="会计分录")
    attachments: Optional[List[str]] = Field(default_factory=list, description="附件")


class VoucherResponse(BaseSchema):
    """凭证响应"""
    id: str
    voucher_date: datetime
    voucher_number: str
    description: str
    status: VoucherStatus
    entries: List[AccountingEntry]
    attachments: List[str]
    total_debit: float
    total_credit: float
    created_by_agent: AgentType
    reviewed_by_agent: Optional[AgentType]


# Agent 相关模型
class AgentConfig(BaseModel):
    """Agent 配置"""
    agent_type: AgentType
    model_provider: str = Field(default="openai", description="模型提供商")
    model_name: str = Field(default="gpt-4-turbo-preview", description="模型名称")
    temperature: float = Field(default=0.1, description="温度参数")
    max_tokens: Optional[int] = Field(default=None, description="最大token数")
    system_prompt: Optional[str] = Field(None, description="系统提示")


class AgentResponse(BaseModel):
    """Agent 响应"""
    agent_type: AgentType
    response: str
    confidence: Optional[float] = Field(None, description="置信度")
    reasoning: Optional[str] = Field(None, description="推理过程")
    suggested_actions: Optional[List[str]] = Field(default_factory=list, description="建议操作")


# 文件上传相关模型
class FileUploadResponse(BaseModel):
    """文件上传响应"""
    filename: str
    file_path: str
    file_size: int
    content_type: str
    upload_time: datetime


# API 响应模型
class APIResponse(BaseModel):
    """通用API响应"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    error: Optional[str] = None


# 工作流相关模型
class WorkflowState(BaseModel):
    """工作流状态"""
    conversation_id: str
    current_step: str
    financial_agent_response: Optional[str] = None
    audit_agent_response: Optional[str] = None
    voucher_draft: Optional[VoucherCreate] = None
    human_feedback: Optional[str] = None
    is_approved: Optional[bool] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
