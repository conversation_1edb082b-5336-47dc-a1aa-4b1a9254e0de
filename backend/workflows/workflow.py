"""
工作流定义
财务会计记账凭证生成的核心工作流
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import json
import asyncio

from models.schemas import WorkflowState, AgentType, VoucherCreate
from agents.financial_agent import FinancialAgent
from agents.audit_agent import AuditAgent
from services.memory_service import MemoryService


class AccountingWorkflowState(BaseModel):
    """会计工作流状态"""
    conversation_id: str
    messages: List[Dict[str, Any]] = []
    current_step: str = "start"
    user_input: Optional[str] = None
    financial_agent_response: Optional[str] = None
    audit_agent_response: Optional[str] = None
    voucher_draft: Optional[Dict[str, Any]] = None
    audit_feedback: Optional[str] = None
    human_feedback: Optional[str] = None
    is_approved: bool = False
    requires_human_review: bool = False
    iteration_count: int = 0
    max_iterations: int = 3
    metadata: Dict[str, Any] = {}


class AccountingWorkflow:
    """财务会计工作流"""

    def __init__(self):
        self.financial_agent = FinancialAgent()
        self.audit_agent = AuditAgent()
        self.memory_service = MemoryService()

    async def _execute_step(self, step_name: str, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """执行工作流步骤"""
        if step_name == "financial_analysis":
            return await self._financial_analysis_node(state)
        elif step_name == "generate_voucher":
            return await self._generate_voucher_node(state)
        elif step_name == "audit_review":
            return await self._audit_review_node(state)
        elif step_name == "human_review":
            return await self._human_review_node(state)
        elif step_name == "finalize_voucher":
            return await self._finalize_voucher_node(state)
        elif step_name == "revision":
            return await self._revision_node(state)
        else:
            state.current_step = "error"
            state.metadata["error"] = f"Unknown step: {step_name}"
            return state
    
    async def _financial_analysis_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """财务分析节点"""
        try:
            # 获取历史记忆
            memory_context = await self.memory_service.get_conversation_memory(
                state.conversation_id
            )
            
            # 财务 Agent 分析
            financial_response = await self.financial_agent.analyze_transaction(
                user_input=state.user_input,
                memory_context=memory_context,
                previous_feedback=state.audit_feedback or state.human_feedback
            )
            
            state.financial_agent_response = financial_response
            state.current_step = "financial_analysis_complete"
            
            # 保存到记忆
            await self.memory_service.add_memory(
                conversation_id=state.conversation_id,
                agent_type=AgentType.FINANCIAL,
                content=financial_response,
                metadata={"step": "financial_analysis"}
            )
            
            return state
            
        except Exception as e:
            state.metadata["error"] = str(e)
            state.current_step = "error"
            return state
    
    async def _generate_voucher_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """生成凭证节点"""
        try:
            # 基于财务分析生成凭证
            voucher_data = await self.financial_agent.generate_voucher(
                analysis_result=state.financial_agent_response,
                conversation_id=state.conversation_id
            )
            
            state.voucher_draft = voucher_data
            state.current_step = "voucher_generated"
            
            return state
            
        except Exception as e:
            state.metadata["error"] = str(e)
            state.current_step = "error"
            return state
    
    async def _audit_review_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """审计审查节点"""
        try:
            # 审计 Agent 审查凭证
            audit_result = await self.audit_agent.review_voucher(
                voucher_data=state.voucher_draft,
                financial_analysis=state.financial_agent_response,
                conversation_id=state.conversation_id
            )
            
            state.audit_agent_response = audit_result["feedback"]
            state.is_approved = audit_result["approved"]
            state.requires_human_review = audit_result["requires_human_review"]
            state.current_step = "audit_complete"
            
            # 保存到记忆
            await self.memory_service.add_memory(
                conversation_id=state.conversation_id,
                agent_type=AgentType.AUDIT,
                content=audit_result["feedback"],
                metadata={"step": "audit_review", "approved": audit_result["approved"]}
            )
            
            return state
            
        except Exception as e:
            state.metadata["error"] = str(e)
            state.current_step = "error"
            return state
    
    async def _human_review_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """人工审查节点"""
        # 这个节点会暂停工作流，等待人工输入
        state.current_step = "waiting_for_human"
        return state
    
    async def _finalize_voucher_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """最终确认凭证节点"""
        try:
            # 最终确认并保存凭证
            final_voucher = await self.financial_agent.finalize_voucher(
                voucher_data=state.voucher_draft,
                audit_feedback=state.audit_agent_response,
                human_feedback=state.human_feedback
            )
            
            state.voucher_draft = final_voucher
            state.current_step = "completed"
            state.is_approved = True
            
            return state
            
        except Exception as e:
            state.metadata["error"] = str(e)
            state.current_step = "error"
            return state
    
    async def _revision_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """修订节点"""
        state.iteration_count += 1
        state.current_step = "revision"
        
        # 如果超过最大迭代次数，强制进入人工审查
        if state.iteration_count >= state.max_iterations:
            state.requires_human_review = True
        
        return state
    
    def _should_require_human_review(self, state: AccountingWorkflowState) -> str:
        """判断是否需要人工审查"""
        if state.requires_human_review:
            return "human_review"
        elif state.is_approved:
            return "finalize"
        else:
            return "revise"
    
    def _process_human_feedback(self, state: AccountingWorkflowState) -> str:
        """处理人工反馈"""
        if not state.human_feedback:
            return "end"
        
        # 解析人工反馈
        feedback_data = json.loads(state.human_feedback) if isinstance(state.human_feedback, str) else state.human_feedback
        
        if feedback_data.get("action") == "approve":
            return "approved"
        elif feedback_data.get("action") == "revise":
            return "revise"
        else:
            return "end"
    
    async def run_workflow(self, conversation_id: str, user_input: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行工作流"""
        initial_state = AccountingWorkflowState(
            conversation_id=conversation_id,
            user_input=user_input,
            metadata=metadata or {}
        )
        
        # 运行图
        result = await self.graph.ainvoke(initial_state)
        
        return {
            "conversation_id": conversation_id,
            "final_state": result.dict(),
            "voucher_draft": result.voucher_draft,
            "is_approved": result.is_approved,
            "current_step": result.current_step
        }
    
    async def resume_workflow(self, conversation_id: str, human_feedback: Dict[str, Any]) -> Dict[str, Any]:
        """恢复工作流（处理人工反馈后）"""
        # 获取当前状态
        current_state = await self.memory_service.get_workflow_state(conversation_id)
        
        if current_state and current_state.current_step == "waiting_for_human":
            current_state.human_feedback = json.dumps(human_feedback)
            
            # 从人工审查节点继续
            result = await self.graph.ainvoke(current_state)
            
            return {
                "conversation_id": conversation_id,
                "final_state": result.dict(),
                "voucher_draft": result.voucher_draft,
                "is_approved": result.is_approved,
                "current_step": result.current_step
            }
        
        raise ValueError("工作流状态不正确，无法恢复")
