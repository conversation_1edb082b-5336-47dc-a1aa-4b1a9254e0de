"""
LangGraph 工作流定义
财务会计记账凭证生成的核心工作流
"""
from typing import Dict, Any, List, Optional, TypedDict, Annotated
from langgraph.graph import StateGraph, END, START
from langgraph.graph.message import add_messages
import json

from models.schemas import AgentType, VoucherCreate
from agents.financial_agent import FinancialAgent
from agents.audit_agent import AuditAgent
from services.memory_service import MemoryService


class AccountingWorkflowState(TypedDict):
    """会计工作流状态"""
    conversation_id: str
    messages: Annotated[List[Dict[str, Any]], add_messages]
    current_step: str
    user_input: Optional[str]
    financial_agent_response: Optional[str]
    audit_agent_response: Optional[str]
    voucher_draft: Optional[Dict[str, Any]]
    audit_feedback: Optional[str]
    human_feedback: Optional[str]
    is_approved: bool
    requires_human_review: bool
    iteration_count: int
    max_iterations: int
    metadata: Dict[str, Any]


class AccountingWorkflow:
    """财务会计工作流"""

    def __init__(self):
        self.financial_agent = FinancialAgent()
        self.audit_agent = AuditAgent()
        self.memory_service = MemoryService()
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建工作流图"""
        # 创建状态图
        workflow = StateGraph(AccountingWorkflowState)

        # 添加节点
        workflow.add_node("financial_analysis", self._financial_analysis_node)
        workflow.add_node("generate_voucher", self._generate_voucher_node)
        workflow.add_node("audit_review", self._audit_review_node)
        workflow.add_node("human_review", self._human_review_node)
        workflow.add_node("finalize_voucher", self._finalize_voucher_node)
        workflow.add_node("revision", self._revision_node)

        # 添加边
        workflow.add_edge(START, "financial_analysis")
        workflow.add_edge("financial_analysis", "generate_voucher")
        workflow.add_edge("generate_voucher", "audit_review")

        # 添加条件边
        workflow.add_conditional_edges(
            "audit_review",
            self._should_require_human_review,
            {
                "human_review": "human_review",
                "finalize": "finalize_voucher",
                "revise": "revision"
            }
        )

        workflow.add_conditional_edges(
            "human_review",
            self._process_human_feedback,
            {
                "approved": "finalize_voucher",
                "revise": "revision",
                "end": END
            }
        )

        workflow.add_edge("revision", "financial_analysis")
        workflow.add_edge("finalize_voucher", END)

        return workflow.compile()
    
    async def _financial_analysis_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """财务分析节点"""
        try:
            # 获取历史记忆
            memory_context = await self.memory_service.get_conversation_memory(
                state["conversation_id"]
            )

            # 财务 Agent 分析
            financial_response = await self.financial_agent.analyze_transaction(
                user_input=state["user_input"],
                memory_context=memory_context,
                previous_feedback=state.get("audit_feedback") or state.get("human_feedback")
            )

            # 更新状态
            return {
                **state,
                "financial_agent_response": financial_response,
                "current_step": "financial_analysis_complete"
            }

        except Exception as e:
            return {
                **state,
                "current_step": "error",
                "metadata": {**state.get("metadata", {}), "error": str(e)}
            }
    
    async def _generate_voucher_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """生成凭证节点"""
        try:
            # 基于财务分析生成凭证
            voucher_data = await self.financial_agent.generate_voucher(
                analysis_result=state["financial_agent_response"],
                conversation_id=state["conversation_id"]
            )

            return {
                **state,
                "voucher_draft": voucher_data,
                "current_step": "voucher_generated"
            }

        except Exception as e:
            return {
                **state,
                "current_step": "error",
                "metadata": {**state.get("metadata", {}), "error": str(e)}
            }
    
    async def _audit_review_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """审计审查节点"""
        try:
            # 审计 Agent 审查凭证
            audit_result = await self.audit_agent.review_voucher(
                voucher_data=state["voucher_draft"],
                financial_analysis=state["financial_agent_response"],
                conversation_id=state["conversation_id"]
            )

            return {
                **state,
                "audit_agent_response": audit_result["feedback"],
                "is_approved": audit_result["approved"],
                "requires_human_review": audit_result["requires_human_review"],
                "current_step": "audit_complete"
            }

        except Exception as e:
            return {
                **state,
                "current_step": "error",
                "metadata": {**state.get("metadata", {}), "error": str(e)}
            }
    
    async def _human_review_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """人工审查节点"""
        # 这个节点会暂停工作流，等待人工输入
        return {
            **state,
            "current_step": "waiting_for_human"
        }

    async def _finalize_voucher_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """最终确认凭证节点"""
        try:
            # 最终确认并保存凭证
            final_voucher = await self.financial_agent.finalize_voucher(
                voucher_data=state["voucher_draft"],
                audit_feedback=state.get("audit_agent_response"),
                human_feedback=state.get("human_feedback")
            )

            return {
                **state,
                "voucher_draft": final_voucher,
                "current_step": "completed",
                "is_approved": True
            }

        except Exception as e:
            return {
                **state,
                "current_step": "error",
                "metadata": {**state.get("metadata", {}), "error": str(e)}
            }

    async def _revision_node(self, state: AccountingWorkflowState) -> AccountingWorkflowState:
        """修订节点"""
        iteration_count = state.get("iteration_count", 0) + 1
        max_iterations = state.get("max_iterations", 3)

        return {
            **state,
            "iteration_count": iteration_count,
            "current_step": "revision",
            "requires_human_review": iteration_count >= max_iterations
        }
    
    def _should_require_human_review(self, state: AccountingWorkflowState) -> str:
        """判断是否需要人工审查"""
        if state.get("requires_human_review", False):
            return "human_review"
        elif state.get("is_approved", False):
            return "finalize"
        else:
            return "revise"

    def _process_human_feedback(self, state: AccountingWorkflowState) -> str:
        """处理人工反馈"""
        human_feedback = state.get("human_feedback")
        if not human_feedback:
            return "end"

        # 解析人工反馈
        try:
            feedback_data = json.loads(human_feedback) if isinstance(human_feedback, str) else human_feedback

            if feedback_data.get("action") == "approve":
                return "approved"
            elif feedback_data.get("action") == "revise":
                return "revise"
            else:
                return "end"
        except:
            return "end"
    
    async def run_workflow(self, conversation_id: str, user_input: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行工作流"""
        initial_state: AccountingWorkflowState = {
            "conversation_id": conversation_id,
            "messages": [],
            "current_step": "start",
            "user_input": user_input,
            "financial_agent_response": None,
            "audit_agent_response": None,
            "voucher_draft": None,
            "audit_feedback": None,
            "human_feedback": None,
            "is_approved": False,
            "requires_human_review": False,
            "iteration_count": 0,
            "max_iterations": 3,
            "metadata": metadata or {}
        }

        # 运行图
        result = await self.graph.ainvoke(initial_state)

        return {
            "conversation_id": conversation_id,
            "final_state": result,
            "voucher_draft": result.get("voucher_draft"),
            "is_approved": result.get("is_approved", False),
            "current_step": result.get("current_step", "unknown")
        }
    
    async def resume_workflow(self, conversation_id: str, human_feedback: Dict[str, Any]) -> Dict[str, Any]:
        """恢复工作流（处理人工反馈后）"""
        # 获取当前状态
        current_state = await self.memory_service.get_workflow_state(conversation_id)

        if current_state and current_state.get("current_step") == "waiting_for_human":
            # 更新状态，添加人工反馈
            updated_state = {
                **current_state,
                "human_feedback": json.dumps(human_feedback)
            }

            # 从人工审查节点继续
            result = await self.graph.ainvoke(updated_state)

            return {
                "conversation_id": conversation_id,
                "final_state": result,
                "voucher_draft": result.get("voucher_draft"),
                "is_approved": result.get("is_approved", False),
                "current_step": result.get("current_step", "unknown")
            }

        raise ValueError("工作流状态不正确，无法恢复")
