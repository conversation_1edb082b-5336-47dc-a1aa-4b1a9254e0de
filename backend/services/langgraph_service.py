"""
LangGraph 服务 - 管理工作流执行
"""
from typing import Dict, Any, Optional, List
from datetime import datetime

from workflows.workflow import AccountingWorkflow
from services.memory_service import MemoryService
from models.schemas import WorkflowState


class LangGraphService:
    """LangGraph 工作流服务"""
    
    def __init__(self):
        self.workflow = AccountingWorkflow()
        self.memory_service = MemoryService()
        # 存储运行中的工作流实例
        self.running_workflows: Dict[str, Dict[str, Any]] = {}
    
    async def process_message(
        self,
        conversation_id: str,
        user_message: str,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理用户消息，触发工作流"""
        
        try:
            # 检查是否有正在运行的工作流
            if conversation_id in self.running_workflows:
                current_workflow = self.running_workflows[conversation_id]
                
                # 如果工作流在等待人工反馈
                if current_workflow.get("status") == "waiting_for_human":
                    return await self._handle_human_feedback(
                        conversation_id,
                        user_message,
                        metadata
                    )
            
            # 启动新的工作流
            result = await self.workflow.run_workflow(
                conversation_id=conversation_id,
                user_input=user_message,
                metadata=metadata or {}
            )
            
            # 保存工作流状态
            self.running_workflows[conversation_id] = {
                "status": "running",
                "result": result,
                "started_at": datetime.now().isoformat()
            }
            
            # 保存到持久化存储
            await self.memory_service.save_workflow_state(
                conversation_id,
                result["final_state"]
            )
            
            return result
            
        except Exception as e:
            error_result = {
                "conversation_id": conversation_id,
                "error": str(e),
                "current_step": "error",
                "is_approved": False
            }
            
            self.running_workflows[conversation_id] = {
                "status": "error",
                "result": error_result,
                "error": str(e)
            }
            
            return error_result
    
    async def _handle_human_feedback(
        self,
        conversation_id: str,
        feedback_message: str,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理人工反馈"""
        
        try:
            # 解析反馈内容
            feedback_data = self._parse_feedback_message(feedback_message, metadata)
            
            # 恢复工作流
            result = await self.workflow.resume_workflow(
                conversation_id=conversation_id,
                human_feedback=feedback_data
            )
            
            # 更新工作流状态
            self.running_workflows[conversation_id] = {
                "status": "completed" if result.get("is_approved") else "running",
                "result": result,
                "updated_at": datetime.now().isoformat()
            }
            
            # 保存到持久化存储
            await self.memory_service.save_workflow_state(
                conversation_id,
                result["final_state"]
            )
            
            return result
            
        except Exception as e:
            error_result = {
                "conversation_id": conversation_id,
                "error": f"处理人工反馈时出错: {str(e)}",
                "current_step": "error"
            }
            
            self.running_workflows[conversation_id]["status"] = "error"
            self.running_workflows[conversation_id]["error"] = str(e)
            
            return error_result
    
    def _parse_feedback_message(
        self,
        message: str,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """解析反馈消息"""
        
        message_lower = message.lower()
        
        # 简单的关键词匹配
        if any(word in message_lower for word in ["同意", "批准", "通过", "approve", "yes"]):
            return {
                "action": "approve",
                "feedback": message,
                "metadata": metadata or {}
            }
        elif any(word in message_lower for word in ["修改", "修订", "revise", "change"]):
            return {
                "action": "revise",
                "feedback": message,
                "metadata": metadata or {}
            }
        elif any(word in message_lower for word in ["拒绝", "reject", "no"]):
            return {
                "action": "reject",
                "feedback": message,
                "metadata": metadata or {}
            }
        else:
            # 默认作为修订建议
            return {
                "action": "revise",
                "feedback": message,
                "metadata": metadata or {}
            }
    
    async def submit_human_feedback(
        self,
        conversation_id: str,
        feedback: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提交结构化的人工反馈"""
        
        try:
            # 恢复工作流
            result = await self.workflow.resume_workflow(
                conversation_id=conversation_id,
                human_feedback=feedback
            )
            
            # 更新工作流状态
            if conversation_id in self.running_workflows:
                self.running_workflows[conversation_id].update({
                    "status": "completed" if result.get("is_approved") else "running",
                    "result": result,
                    "updated_at": datetime.now().isoformat()
                })
            
            # 保存到持久化存储
            await self.memory_service.save_workflow_state(
                conversation_id,
                result["final_state"]
            )
            
            return result
            
        except Exception as e:
            return {
                "conversation_id": conversation_id,
                "error": f"提交反馈时出错: {str(e)}",
                "current_step": "error"
            }
    
    async def get_workflow_state(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        
        # 先从内存中获取
        if conversation_id in self.running_workflows:
            return self.running_workflows[conversation_id]["result"]
        
        # 从持久化存储中获取
        state = await self.memory_service.get_workflow_state(conversation_id)
        if state:
            return state.model_dump() if hasattr(state, 'model_dump') else state.dict()
        
        return None
    
    async def cancel_workflow(self, conversation_id: str) -> bool:
        """取消工作流"""
        
        if conversation_id in self.running_workflows:
            self.running_workflows[conversation_id]["status"] = "cancelled"
            self.running_workflows[conversation_id]["cancelled_at"] = datetime.now().isoformat()
            return True
        
        return False
    
    async def get_workflow_history(self, conversation_id: str) -> List[Dict[str, Any]]:
        """获取工作流历史"""
        
        # 这里可以从数据库或日志中获取历史记录
        # 简化实现，返回当前状态
        current_state = await self.get_workflow_state(conversation_id)
        
        if current_state:
            return [current_state]
        
        return []
    
    async def get_active_workflows(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活跃的工作流"""
        
        active_workflows = {}
        
        for conversation_id, workflow_info in self.running_workflows.items():
            if workflow_info["status"] in ["running", "waiting_for_human"]:
                active_workflows[conversation_id] = workflow_info
        
        return active_workflows
    
    async def cleanup_completed_workflows(self, max_age_hours: int = 24) -> int:
        """清理已完成的工作流"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        cleaned_count = 0
        
        workflows_to_remove = []
        
        for conversation_id, workflow_info in self.running_workflows.items():
            if workflow_info["status"] in ["completed", "error", "cancelled"]:
                # 检查完成时间
                completed_time_str = workflow_info.get("updated_at") or workflow_info.get("started_at")
                if completed_time_str:
                    completed_time = datetime.fromisoformat(completed_time_str)
                    if completed_time < cutoff_time:
                        workflows_to_remove.append(conversation_id)
        
        # 移除过期的工作流
        for conversation_id in workflows_to_remove:
            del self.running_workflows[conversation_id]
            cleaned_count += 1
        
        return cleaned_count
