"""
凭证服务 - 管理记账凭证
"""
from typing import Dict, Any, List, Optional
import uuid
from datetime import datetime

from models.schemas import VoucherCreate, VoucherStatus, AgentType
from utils.accounting_tools import AccountingTools


class VoucherService:
    """凭证管理服务"""
    
    def __init__(self):
        self.accounting_tools = AccountingTools()
        # 使用内存存储，实际应用中应该使用数据库
        self.vouchers: Dict[str, Dict[str, Any]] = {}
    
    async def create_voucher(
        self,
        voucher_id: str,
        voucher_data: VoucherCreate
    ) -> Dict[str, Any]:
        """创建记账凭证"""
        
        # 验证凭证数据
        validation_errors = self.accounting_tools.validate_voucher_entries(
            voucher_data.entries
        )
        
        if validation_errors:
            raise ValueError(f"凭证验证失败: {'; '.join(validation_errors)}")
        
        # 生成凭证号
        voucher_number = voucher_data.voucher_number or \
                        self.accounting_tools.generate_voucher_number(
                            voucher_data.voucher_date
                        )
        
        # 计算借贷总额
        balance_check = self.accounting_tools.validate_debit_credit_balance(
            voucher_data.entries
        )
        
        voucher = {
            "id": voucher_id,
            "voucher_date": voucher_data.voucher_date,
            "voucher_number": voucher_number,
            "description": voucher_data.description,
            "status": VoucherStatus.DRAFT,
            "entries": [entry.dict() if hasattr(entry, 'dict') else entry 
                       for entry in voucher_data.entries],
            "attachments": voucher_data.attachments or [],
            "total_debit": balance_check["total_debit"],
            "total_credit": balance_check["total_credit"],
            "created_by_agent": AgentType.FINANCIAL,
            "reviewed_by_agent": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        self.vouchers[voucher_id] = voucher
        return voucher
    
    async def get_voucher(self, voucher_id: str) -> Optional[Dict[str, Any]]:
        """获取凭证详情"""
        return self.vouchers.get(voucher_id)
    
    async def list_vouchers(
        self,
        skip: int = 0,
        limit: int = 20,
        filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取凭证列表"""
        
        vouchers = list(self.vouchers.values())
        
        # 应用过滤器
        if filters:
            if filters.get("status"):
                vouchers = [v for v in vouchers if v["status"] == filters["status"]]
            
            if filters.get("created_by_agent"):
                vouchers = [v for v in vouchers 
                           if v["created_by_agent"] == filters["created_by_agent"]]
            
            if filters.get("date_from"):
                vouchers = [v for v in vouchers 
                           if v["voucher_date"] >= filters["date_from"]]
            
            if filters.get("date_to"):
                vouchers = [v for v in vouchers 
                           if v["voucher_date"] <= filters["date_to"]]
        
        # 按创建时间倒序排列
        vouchers.sort(key=lambda x: x["created_at"], reverse=True)
        
        return vouchers[skip:skip + limit]
    
    async def update_voucher(
        self,
        voucher_id: str,
        voucher_data: VoucherCreate
    ) -> Optional[Dict[str, Any]]:
        """更新凭证"""
        
        if voucher_id not in self.vouchers:
            return None
        
        # 验证凭证数据
        validation_errors = self.accounting_tools.validate_voucher_entries(
            voucher_data.entries
        )
        
        if validation_errors:
            raise ValueError(f"凭证验证失败: {'; '.join(validation_errors)}")
        
        # 计算借贷总额
        balance_check = self.accounting_tools.validate_debit_credit_balance(
            voucher_data.entries
        )
        
        # 更新凭证
        voucher = self.vouchers[voucher_id]
        voucher.update({
            "voucher_date": voucher_data.voucher_date,
            "description": voucher_data.description,
            "entries": [entry.dict() if hasattr(entry, 'dict') else entry 
                       for entry in voucher_data.entries],
            "attachments": voucher_data.attachments or [],
            "total_debit": balance_check["total_debit"],
            "total_credit": balance_check["total_credit"],
            "updated_at": datetime.now().isoformat()
        })
        
        return voucher
    
    async def delete_voucher(self, voucher_id: str) -> bool:
        """删除凭证"""
        if voucher_id in self.vouchers:
            del self.vouchers[voucher_id]
            return True
        return False
    
    async def approve_voucher(
        self,
        voucher_id: str,
        approver_agent: AgentType,
        comments: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """审批凭证"""
        
        if voucher_id not in self.vouchers:
            return None
        
        voucher = self.vouchers[voucher_id]
        voucher.update({
            "status": VoucherStatus.APPROVED,
            "reviewed_by_agent": approver_agent,
            "approval_comments": comments,
            "approved_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        })
        
        return voucher
    
    async def reject_voucher(
        self,
        voucher_id: str,
        reviewer_agent: AgentType,
        reason: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """拒绝凭证"""
        
        if voucher_id not in self.vouchers:
            return None
        
        voucher = self.vouchers[voucher_id]
        voucher.update({
            "status": VoucherStatus.REJECTED,
            "reviewed_by_agent": reviewer_agent,
            "rejection_reason": reason,
            "rejected_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        })
        
        return voucher
    
    async def get_voucher_history(self, voucher_id: str) -> List[Dict[str, Any]]:
        """获取凭证历史记录"""
        # 简化实现，返回当前状态
        voucher = self.vouchers.get(voucher_id)
        if voucher:
            return [voucher]
        return []
    
    async def export_voucher(
        self,
        voucher_id: str,
        format: str = "pdf"
    ) -> Dict[str, Any]:
        """导出凭证"""
        voucher = self.vouchers.get(voucher_id)
        if not voucher:
            raise ValueError("凭证不存在")
        
        # 这里应该实现真实的导出逻辑
        # 简化实现，返回导出信息
        return {
            "voucher_id": voucher_id,
            "format": format,
            "export_url": f"/exports/voucher_{voucher_id}.{format}",
            "exported_at": datetime.now().isoformat()
        }
    
    async def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取凭证统计信息"""
        vouchers = list(self.vouchers.values())
        
        total_vouchers = len(vouchers)
        approved_vouchers = len([v for v in vouchers if v["status"] == VoucherStatus.APPROVED])
        pending_vouchers = len([v for v in vouchers if v["status"] == VoucherStatus.PENDING_REVIEW])
        rejected_vouchers = len([v for v in vouchers if v["status"] == VoucherStatus.REJECTED])
        
        return {
            "total_vouchers": total_vouchers,
            "approved_vouchers": approved_vouchers,
            "pending_vouchers": pending_vouchers,
            "rejected_vouchers": rejected_vouchers,
            "success_rate": approved_vouchers / total_vouchers * 100 if total_vouchers > 0 else 0,
            "period_days": days
        }
    
    async def batch_approve_vouchers(
        self,
        voucher_ids: List[str],
        approver_agent: AgentType,
        comments: Optional[str] = None
    ) -> Dict[str, Any]:
        """批量审批凭证"""
        
        success_count = 0
        failed_count = 0
        results = []
        
        for voucher_id in voucher_ids:
            try:
                result = await self.approve_voucher(voucher_id, approver_agent, comments)
                if result:
                    success_count += 1
                    results.append({"voucher_id": voucher_id, "status": "success"})
                else:
                    failed_count += 1
                    results.append({"voucher_id": voucher_id, "status": "not_found"})
            except Exception as e:
                failed_count += 1
                results.append({"voucher_id": voucher_id, "status": "error", "error": str(e)})
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "results": results
        }
