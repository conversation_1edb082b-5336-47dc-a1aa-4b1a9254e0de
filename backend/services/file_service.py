"""
文件服务 - 管理文件上传和处理
"""
from typing import Dict, Any, List, Optional
import os
import uuid
import shutil
from datetime import datetime
from fastapi import UploadFile

from config.settings import settings


class FileService:
    """文件管理服务"""
    
    def __init__(self):
        # 使用内存存储，实际应用中应该使用数据库
        self.files: Dict[str, Dict[str, Any]] = {}
        
        # 确保上传目录存在
        os.makedirs(settings.upload_dir, exist_ok=True)
    
    async def save_uploaded_file(
        self,
        file: UploadFile,
        description: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """保存上传的文件"""
        
        # 生成文件ID和路径
        file_id = str(uuid.uuid4())
        file_ext = os.path.splitext(file.filename or "")[1]
        safe_filename = f"{file_id}{file_ext}"
        file_path = os.path.join(settings.upload_dir, safe_filename)
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        # 创建文件记录
        file_record = {
            "id": file_id,
            "filename": file.filename,
            "original_filename": file.filename,
            "file_path": file_path,
            "file_size": file_size,
            "content_type": file.content_type,
            "description": description,
            "conversation_id": conversation_id,
            "upload_time": datetime.now().isoformat(),
            "processed": False
        }
        
        self.files[file_id] = file_record
        
        return {
            "file_id": file_id,
            "filename": file.filename,
            "file_path": file_path,
            "file_size": file_size,
            "content_type": file.content_type,
            "upload_time": file_record["upload_time"]
        }
    
    async def get_file_info(self, file_id: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        return self.files.get(file_id)
    
    async def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        file_record = self.files.get(file_id)
        if not file_record:
            return False
        
        # 删除物理文件
        try:
            if os.path.exists(file_record["file_path"]):
                os.remove(file_record["file_path"])
        except Exception as e:
            print(f"删除文件失败: {e}")
        
        # 删除记录
        del self.files[file_id]
        return True
    
    async def list_files(
        self,
        conversation_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取文件列表"""
        
        files = list(self.files.values())
        
        # 过滤对话ID
        if conversation_id:
            files = [f for f in files if f.get("conversation_id") == conversation_id]
        
        # 按上传时间倒序排列
        files.sort(key=lambda x: x["upload_time"], reverse=True)
        
        return files[skip:skip + limit]
    
    async def process_file(
        self,
        file_id: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理文件（预留接口）"""
        
        file_record = self.files.get(file_id)
        if not file_record:
            raise ValueError("文件不存在")
        
        # 这里是文件处理的预留接口
        # 可以实现：
        # - PDF文档解析
        # - OCR文字识别
        # - 表格数据提取
        # - 发票信息识别
        # - 银行对账单解析
        
        processing_type = options.get("type", "auto")
        
        # 模拟处理结果
        result = {
            "file_id": file_id,
            "processing_type": processing_type,
            "status": "completed",
            "extracted_data": {
                "text": "这是从文件中提取的文本内容...",
                "tables": [],
                "metadata": {
                    "pages": 1,
                    "words": 100
                }
            },
            "processed_at": datetime.now().isoformat()
        }
        
        # 更新文件记录
        file_record["processed"] = True
        file_record["processing_result"] = result
        
        return result
    
    async def generate_preview(self, file_id: str) -> Dict[str, Any]:
        """生成文件预览（预留接口）"""
        
        file_record = self.files.get(file_id)
        if not file_record:
            raise ValueError("文件不存在")
        
        # 这里应该根据文件类型生成预览
        # 简化实现，返回基本信息
        return {
            "file_id": file_id,
            "preview_type": "text",
            "content": "文件预览功能正在开发中...",
            "thumbnail_url": None,
            "generated_at": datetime.now().isoformat()
        }
    
    async def extract_financial_data(
        self,
        file: UploadFile,
        extraction_type: str = "auto"
    ) -> Dict[str, Any]:
        """从文件中提取财务数据（预留接口）"""
        
        # 这是一个重要的预留接口，用于从各种财务文档中提取数据
        # 可以支持：
        # - 发票数据提取
        # - 银行对账单解析
        # - 财务报表数据提取
        # - 收据信息识别
        
        # 模拟提取结果
        extracted_data = {
            "extraction_type": extraction_type,
            "confidence": 0.95,
            "data": {
                "document_type": "invoice",  # invoice, bank_statement, receipt, etc.
                "amount": 1000.00,
                "date": "2024-01-15",
                "vendor": "示例供应商",
                "description": "办公用品采购",
                "tax_amount": 130.00,
                "currency": "CNY",
                "account_suggestions": [
                    {
                        "account_code": "1403",
                        "account_name": "原材料",
                        "debit_amount": 1000.00
                    },
                    {
                        "account_code": "2221",
                        "account_name": "应交税费-应交增值税",
                        "debit_amount": 130.00
                    },
                    {
                        "account_code": "2201",
                        "account_name": "应付账款",
                        "credit_amount": 1130.00
                    }
                ]
            },
            "extracted_at": datetime.now().isoformat()
        }
        
        return extracted_data
    
    async def get_file_statistics(self) -> Dict[str, Any]:
        """获取文件统计信息"""
        
        files = list(self.files.values())
        
        total_files = len(files)
        total_size = sum(f["file_size"] for f in files)
        processed_files = len([f for f in files if f.get("processed", False)])
        
        # 按文件类型统计
        type_stats = {}
        for file_record in files:
            content_type = file_record.get("content_type", "unknown")
            type_stats[content_type] = type_stats.get(content_type, 0) + 1
        
        return {
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "processed_files": processed_files,
            "processing_rate": processed_files / total_files * 100 if total_files > 0 else 0,
            "type_distribution": type_stats
        }
