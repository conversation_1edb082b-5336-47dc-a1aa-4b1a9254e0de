"""
Agent 服务 - 管理 AI Agent 配置和操作
"""
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

from models.schemas import AgentType, AgentConfig
from agents.financial_agent import FinancialAgent
from agents.audit_agent import AuditAgent


class AgentService:
    """Agent 管理服务"""
    
    def __init__(self):
        self.financial_agent = FinancialAgent()
        self.audit_agent = AuditAgent()
        
        # 默认配置
        self.default_configs = {
            AgentType.FINANCIAL: AgentConfig(
                agent_type=AgentType.FINANCIAL,
                model_provider="openai",
                model_name="gpt-4-turbo-preview",
                temperature=0.1,
                max_tokens=2000,
                system_prompt="你是一个专业的财务会计AI助手"
            ),
            AgentType.AUDIT: AgentConfig(
                agent_type=AgentType.AUDIT,
                model_provider="openai", 
                model_name="gpt-4-turbo-preview",
                temperature=0.05,
                max_tokens=1500,
                system_prompt="你是一个专业的财务审计AI助手"
            )
        }
    
    async def get_all_configs(self) -> Dict[str, AgentConfig]:
        """获取所有 Agent 配置"""
        return {
            agent_type.value: config.dict() 
            for agent_type, config in self.default_configs.items()
        }
    
    async def get_config(self, agent_type: AgentType) -> Optional[AgentConfig]:
        """获取特定 Agent 配置"""
        return self.default_configs.get(agent_type)
    
    async def update_config(
        self, 
        agent_type: AgentType, 
        config: AgentConfig
    ) -> AgentConfig:
        """更新 Agent 配置"""
        self.default_configs[agent_type] = config
        
        # 这里应该保存到数据库
        # 简化实现，直接返回配置
        return config
    
    async def test_agent(
        self, 
        agent_type: AgentType, 
        test_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """测试 Agent 功能"""
        try:
            if agent_type == AgentType.FINANCIAL:
                result = await self.financial_agent.analyze_transaction(
                    user_input=test_data.get("input", "测试输入"),
                    memory_context=None
                )
            elif agent_type == AgentType.AUDIT:
                # 创建测试凭证数据
                test_voucher = {
                    "voucher_date": "2024-01-01",
                    "description": "测试凭证",
                    "entries": [
                        {
                            "account_code": "1002",
                            "account_name": "银行存款",
                            "debit_amount": 1000,
                            "credit_amount": None
                        },
                        {
                            "account_code": "5001", 
                            "account_name": "主营业务收入",
                            "debit_amount": None,
                            "credit_amount": 1000
                        }
                    ]
                }
                
                result = await self.audit_agent.review_voucher(
                    voucher_data=test_voucher,
                    financial_analysis="测试分析",
                    conversation_id="test"
                )
            else:
                raise ValueError(f"不支持的 Agent 类型: {agent_type}")
            
            return {
                "success": True,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_available_models(self) -> Dict[str, List[str]]:
        """获取可用的模型列表"""
        return {
            "openai": [
                "gpt-4-turbo-preview",
                "gpt-4",
                "gpt-3.5-turbo",
                "gpt-3.5-turbo-16k"
            ],
            "anthropic": [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229", 
                "claude-3-haiku-20240307"
            ],
            "local": [
                "llama2-7b",
                "llama2-13b",
                "chatglm3-6b"
            ]
        }
    
    async def get_model_providers(self) -> List[Dict[str, Any]]:
        """获取模型提供商列表"""
        return [
            {
                "id": "openai",
                "name": "OpenAI",
                "description": "OpenAI GPT 系列模型",
                "supported": True
            },
            {
                "id": "anthropic", 
                "name": "Anthropic",
                "description": "Anthropic Claude 系列模型",
                "supported": True
            },
            {
                "id": "local",
                "name": "本地模型",
                "description": "本地部署的开源模型",
                "supported": False
            }
        ]
    
    async def switch_model(
        self,
        agent_type: AgentType,
        provider: str,
        model_name: str
    ) -> Dict[str, Any]:
        """切换模型"""
        try:
            # 获取当前配置
            current_config = self.default_configs.get(agent_type)
            if not current_config:
                raise ValueError(f"Agent 类型不存在: {agent_type}")
            
            # 更新配置
            new_config = AgentConfig(
                agent_type=agent_type,
                model_provider=provider,
                model_name=model_name,
                temperature=current_config.temperature,
                max_tokens=current_config.max_tokens,
                system_prompt=current_config.system_prompt
            )
            
            await self.update_config(agent_type, new_config)
            
            return {
                "success": True,
                "message": f"已切换到 {provider}/{model_name}",
                "config": new_config.dict()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_performance_stats(
        self, 
        agent_type: AgentType, 
        days: int = 7
    ) -> Dict[str, Any]:
        """获取 Agent 性能统计"""
        # 这里应该从数据库获取真实的统计数据
        # 简化实现，返回模拟数据
        return {
            "agent_type": agent_type.value,
            "period_days": days,
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "success_rate": 0.0,
            "daily_stats": []
        }
    
    async def submit_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """提交 Agent 反馈"""
        try:
            # 这里应该保存反馈到数据库
            # 简化实现，直接返回成功
            return {
                "success": True,
                "message": "反馈提交成功",
                "feedback_id": "feedback_" + datetime.now().strftime("%Y%m%d_%H%M%S")
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
