"""
记忆服务 - 管理 Agent 的长短期记忆
"""
from typing import Dict, Any, List, Optional
import json
import redis.asyncio as redis
from datetime import datetime, timedelta
import uuid

from config.settings import settings
from models.schemas import AgentType, WorkflowState
from app.database import get_redis


class MemoryService:
    """记忆管理服务"""
    
    def __init__(self):
        self.redis_client = None
        self.short_term_ttl = 3600  # 1小时
        self.medium_term_ttl = 86400 * 7  # 7天
        self.long_term_ttl = 86400 * 30  # 30天
    
    async def _get_redis(self) -> redis.Redis:
        """获取 Redis 连接"""
        if self.redis_client is None:
            self.redis_client = await get_redis()
        return self.redis_client
    
    async def add_memory(
        self,
        conversation_id: str,
        agent_type: AgentType,
        content: str,
        metadata: Dict[str, Any] = None,
        memory_type: str = "short_term"
    ) -> str:
        """添加记忆"""
        redis_client = await self._get_redis()
        
        memory_id = str(uuid.uuid4())
        memory_data = {
            "id": memory_id,
            "conversation_id": conversation_id,
            "agent_type": agent_type.value,
            "content": content,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat(),
            "memory_type": memory_type
        }
        
        # 选择TTL
        ttl_map = {
            "short_term": self.short_term_ttl,
            "medium_term": self.medium_term_ttl,
            "long_term": self.long_term_ttl
        }
        ttl = ttl_map.get(memory_type, self.short_term_ttl)
        
        # 存储记忆
        memory_key = f"memory:{conversation_id}:{agent_type.value}:{memory_id}"
        await redis_client.setex(
            memory_key,
            ttl,
            json.dumps(memory_data, ensure_ascii=False)
        )
        
        # 添加到对话记忆索引
        index_key = f"memory_index:{conversation_id}:{agent_type.value}"
        await redis_client.lpush(index_key, memory_id)
        await redis_client.expire(index_key, ttl)
        
        return memory_id
    
    async def get_conversation_memory(
        self,
        conversation_id: str,
        agent_type: Optional[AgentType] = None,
        limit: int = 10
    ) -> str:
        """获取对话记忆上下文"""
        redis_client = await self._get_redis()
        
        memories = []
        
        if agent_type:
            # 获取特定 Agent 的记忆
            agent_memories = await self._get_agent_memories(
                conversation_id, agent_type, limit
            )
            memories.extend(agent_memories)
        else:
            # 获取所有 Agent 的记忆
            for agent in AgentType:
                agent_memories = await self._get_agent_memories(
                    conversation_id, agent, limit // 2
                )
                memories.extend(agent_memories)
        
        # 按时间排序
        memories.sort(key=lambda x: x["created_at"])
        
        # 构建上下文字符串
        context_parts = []
        for memory in memories[-limit:]:  # 取最近的记忆
            context_parts.append(
                f"[{memory['agent_type']}] {memory['content']}"
            )
        
        return "\n".join(context_parts)
    
    async def _get_agent_memories(
        self,
        conversation_id: str,
        agent_type: AgentType,
        limit: int
    ) -> List[Dict[str, Any]]:
        """获取特定 Agent 的记忆"""
        redis_client = await self._get_redis()
        
        index_key = f"memory_index:{conversation_id}:{agent_type.value}"
        memory_ids = await redis_client.lrange(index_key, 0, limit - 1)
        
        memories = []
        for memory_id in memory_ids:
            memory_key = f"memory:{conversation_id}:{agent_type.value}:{memory_id.decode()}"
            memory_data = await redis_client.get(memory_key)
            
            if memory_data:
                try:
                    memory = json.loads(memory_data)
                    memories.append(memory)
                except json.JSONDecodeError:
                    continue
        
        return memories
    
    async def save_workflow_state(
        self,
        conversation_id: str,
        state: WorkflowState
    ) -> None:
        """保存工作流状态"""
        redis_client = await self._get_redis()
        
        state_key = f"workflow_state:{conversation_id}"
        state_data = state.dict() if hasattr(state, 'dict') else state
        
        await redis_client.setex(
            state_key,
            self.medium_term_ttl,
            json.dumps(state_data, ensure_ascii=False, default=str)
        )
    
    async def get_workflow_state(
        self,
        conversation_id: str
    ) -> Optional[WorkflowState]:
        """获取工作流状态"""
        redis_client = await self._get_redis()
        
        state_key = f"workflow_state:{conversation_id}"
        state_data = await redis_client.get(state_key)
        
        if state_data:
            try:
                state_dict = json.loads(state_data)
                return WorkflowState(**state_dict)
            except (json.JSONDecodeError, TypeError):
                return None
        
        return None
    
    async def update_workflow_state(
        self,
        conversation_id: str,
        updates: Dict[str, Any]
    ) -> Optional[WorkflowState]:
        """更新工作流状态"""
        current_state = await self.get_workflow_state(conversation_id)
        
        if current_state:
            # 更新状态
            state_dict = current_state.dict()
            state_dict.update(updates)
            
            updated_state = WorkflowState(**state_dict)
            await self.save_workflow_state(conversation_id, updated_state)
            
            return updated_state
        
        return None
    
    async def get_similar_cases(
        self,
        query: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """获取相似案例（简化版本，实际应用中可以使用向量搜索）"""
        redis_client = await self._get_redis()
        
        # 这里是一个简化的实现，实际应用中应该使用向量数据库
        # 搜索包含关键词的记忆
        pattern = f"memory:*:*:*"
        keys = await redis_client.keys(pattern)
        
        similar_cases = []
        query_lower = query.lower()
        
        for key in keys[:100]:  # 限制搜索范围
            memory_data = await redis_client.get(key)
            if memory_data:
                try:
                    memory = json.loads(memory_data)
                    content_lower = memory.get("content", "").lower()
                    
                    # 简单的关键词匹配
                    if any(word in content_lower for word in query_lower.split()):
                        similar_cases.append(memory)
                        
                        if len(similar_cases) >= limit:
                            break
                            
                except json.JSONDecodeError:
                    continue
        
        return similar_cases
    
    async def cleanup_expired_memories(self) -> int:
        """清理过期记忆"""
        redis_client = await self._get_redis()
        
        # Redis 会自动清理过期的键，这里主要是清理索引
        pattern = "memory_index:*"
        keys = await redis_client.keys(pattern)
        
        cleaned_count = 0
        for key in keys:
            # 检查索引是否还有有效的记忆
            memory_ids = await redis_client.lrange(key, 0, -1)
            valid_ids = []
            
            for memory_id in memory_ids:
                # 构建记忆键
                key_parts = key.decode().split(":")
                if len(key_parts) >= 3:
                    conversation_id = key_parts[1]
                    agent_type = key_parts[2]
                    memory_key = f"memory:{conversation_id}:{agent_type}:{memory_id.decode()}"
                    
                    if await redis_client.exists(memory_key):
                        valid_ids.append(memory_id)
                    else:
                        cleaned_count += 1
            
            # 更新索引
            if valid_ids:
                await redis_client.delete(key)
                if valid_ids:
                    await redis_client.lpush(key, *valid_ids)
            else:
                await redis_client.delete(key)
        
        return cleaned_count
    
    async def export_conversation_memory(
        self,
        conversation_id: str
    ) -> Dict[str, Any]:
        """导出对话记忆"""
        redis_client = await self._get_redis()
        
        export_data = {
            "conversation_id": conversation_id,
            "exported_at": datetime.now().isoformat(),
            "memories": {}
        }
        
        for agent_type in AgentType:
            agent_memories = await self._get_agent_memories(
                conversation_id, agent_type, 1000  # 导出所有记忆
            )
            export_data["memories"][agent_type.value] = agent_memories
        
        # 获取工作流状态
        workflow_state = await self.get_workflow_state(conversation_id)
        if workflow_state:
            export_data["workflow_state"] = workflow_state.dict()
        
        return export_data
