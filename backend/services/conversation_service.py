"""
对话服务 - 管理对话和消息
"""
from typing import Dict, Any, List, Optional
import uuid
from datetime import datetime

from models.schemas import (
    ConversationCreate,
    ConversationResponse,
    MessageCreate,
    MessageResponse,
    ConversationStatus,
    MessageRole
)
from services.memory_service import MemoryService


class ConversationService:
    """对话管理服务"""
    
    def __init__(self):
        self.memory_service = MemoryService()
        # 这里使用内存存储，实际应用中应该使用数据库
        self.conversations: Dict[str, Dict[str, Any]] = {}
        self.messages: Dict[str, List[Dict[str, Any]]] = {}
    
    async def create_conversation(
        self,
        conversation_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建新对话"""
        
        conversation = {
            "id": conversation_id,
            "title": title or f"对话 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            "description": description,
            "status": ConversationStatus.ACTIVE,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        self.conversations[conversation_id] = conversation
        self.messages[conversation_id] = []
        
        return conversation
    
    async def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取对话详情"""
        
        conversation = self.conversations.get(conversation_id)
        if not conversation:
            return None
        
        # 获取消息列表
        messages = self.messages.get(conversation_id, [])
        
        return {
            **conversation,
            "messages": messages
        }
    
    async def list_conversations(
        self,
        skip: int = 0,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取对话列表"""
        
        conversations = list(self.conversations.values())
        
        # 按创建时间倒序排列
        conversations.sort(
            key=lambda x: x["created_at"],
            reverse=True
        )
        
        return conversations[skip:skip + limit]
    
    async def add_message(
        self,
        conversation_id: str,
        content: str,
        role: MessageRole,
        agent_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """添加消息到对话"""
        
        if conversation_id not in self.conversations:
            raise ValueError("对话不存在")
        
        message_id = str(uuid.uuid4())
        message = {
            "id": message_id,
            "content": content,
            "role": role,
            "agent_type": agent_type,
            "conversation_id": conversation_id,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat()
        }
        
        # 添加到消息列表
        if conversation_id not in self.messages:
            self.messages[conversation_id] = []
        
        self.messages[conversation_id].append(message)
        
        # 更新对话的最后更新时间
        self.conversations[conversation_id]["updated_at"] = datetime.now().isoformat()
        
        return message
    
    async def get_messages(
        self,
        conversation_id: str,
        skip: int = 0,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取对话消息列表"""
        
        messages = self.messages.get(conversation_id, [])
        
        # 按创建时间排序
        messages.sort(key=lambda x: x["created_at"])
        
        return messages[skip:skip + limit]
    
    async def update_conversation_status(
        self,
        conversation_id: str,
        status: ConversationStatus
    ) -> Optional[Dict[str, Any]]:
        """更新对话状态"""
        
        if conversation_id not in self.conversations:
            return None
        
        self.conversations[conversation_id]["status"] = status
        self.conversations[conversation_id]["updated_at"] = datetime.now().isoformat()
        
        return self.conversations[conversation_id]
    
    async def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话"""
        
        if conversation_id not in self.conversations:
            return False
        
        # 删除对话和相关消息
        del self.conversations[conversation_id]
        if conversation_id in self.messages:
            del self.messages[conversation_id]
        
        return True
    
    async def search_conversations(
        self,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索对话"""
        
        results = []
        query_lower = query.lower()
        
        for conversation in self.conversations.values():
            # 搜索标题和描述
            if (query_lower in conversation.get("title", "").lower() or
                query_lower in conversation.get("description", "").lower()):
                results.append(conversation)
                continue
            
            # 搜索消息内容
            conversation_messages = self.messages.get(conversation["id"], [])
            for message in conversation_messages:
                if query_lower in message["content"].lower():
                    results.append(conversation)
                    break
        
        # 按相关性排序（这里简化为按更新时间）
        results.sort(key=lambda x: x["updated_at"], reverse=True)
        
        return results[:limit]
