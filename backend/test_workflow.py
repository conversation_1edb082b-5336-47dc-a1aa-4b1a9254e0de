#!/usr/bin/env python3
"""
测试 LangGraph 工作流
"""
import asyncio
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from langgraph_flows.workflow import AccountingWorkflow


async def test_workflow():
    """测试工作流"""
    print("🧪 测试 LangGraph 工作流...")
    
    try:
        # 创建工作流实例
        workflow = AccountingWorkflow()
        print("✅ 工作流实例创建成功")
        
        # 测试工作流运行
        print("🚀 开始测试工作流运行...")
        
        result = await workflow.run_workflow(
            conversation_id="test-conversation-001",
            user_input="收到客户货款 10000 元，存入银行",
            metadata={"test": True}
        )
        
        print("✅ 工作流运行成功")
        print(f"📊 结果: {result}")
        
        # 检查结果
        if result.get("conversation_id") == "test-conversation-001":
            print("✅ 对话ID正确")
        
        if result.get("final_state"):
            print("✅ 最终状态存在")
            final_state = result["final_state"]
            print(f"   当前步骤: {final_state.get('current_step')}")
            print(f"   是否批准: {final_state.get('is_approved')}")
            
            if final_state.get("financial_agent_response"):
                print("✅ 财务Agent响应存在")
            
            if final_state.get("voucher_draft"):
                print("✅ 凭证草稿生成成功")
        
        print("\n🎉 工作流测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("财务会计 AI Agent 工作流测试")
    print("=" * 60)
    
    success = await test_workflow()
    
    if success:
        print("\n🎊 所有测试通过！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
