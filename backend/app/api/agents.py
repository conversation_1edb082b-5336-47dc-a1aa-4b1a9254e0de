"""
Agent 相关的 API 路由
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import uuid

from models.schemas import (
    AgentConfig,
    AgentResponse,
    APIResponse,
    AgentType
)
from services.agent_service import AgentService

router = APIRouter()

# 依赖注入函数
def get_agent_service():
    return AgentService()


@router.get("/config", response_model=APIResponse)
async def get_agent_configs(
    agent_service: AgentService = Depends(get_agent_service)
):
    """获取所有 Agent 配置"""
    try:
        configs = await agent_service.get_all_configs()
        
        return APIResponse(
            success=True,
            data=configs
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/{agent_type}", response_model=APIResponse)
async def get_agent_config(
    agent_type: AgentType,
    agent_service: AgentService = Depends()
):
    """获取特定 Agent 配置"""
    try:
        config = await agent_service.get_config(agent_type)
        
        return APIResponse(
            success=True,
            data=config
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config/{agent_type}", response_model=APIResponse)
async def update_agent_config(
    agent_type: AgentType,
    config: AgentConfig,
    agent_service: AgentService = Depends()
):
    """更新 Agent 配置"""
    try:
        updated_config = await agent_service.update_config(agent_type, config)
        
        return APIResponse(
            success=True,
            message="配置更新成功",
            data=updated_config
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test/{agent_type}", response_model=APIResponse)
async def test_agent(
    agent_type: AgentType,
    test_data: Dict[str, Any],
    agent_service: AgentService = Depends()
):
    """测试 Agent 功能"""
    try:
        result = await agent_service.test_agent(agent_type, test_data)
        
        return APIResponse(
            success=True,
            message="测试完成",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models", response_model=APIResponse)
async def get_available_models(
    agent_service: AgentService = Depends()
):
    """获取可用的模型列表"""
    try:
        models = await agent_service.get_available_models()
        
        return APIResponse(
            success=True,
            data=models
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers", response_model=APIResponse)
async def get_model_providers(
    agent_service: AgentService = Depends()
):
    """获取模型提供商列表"""
    try:
        providers = await agent_service.get_model_providers()
        
        return APIResponse(
            success=True,
            data=providers
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/switch-model", response_model=APIResponse)
async def switch_model(
    switch_data: Dict[str, Any],
    agent_service: AgentService = Depends()
):
    """切换模型"""
    try:
        result = await agent_service.switch_model(
            agent_type=switch_data.get("agent_type"),
            provider=switch_data.get("provider"),
            model_name=switch_data.get("model_name")
        )
        
        return APIResponse(
            success=True,
            message="模型切换成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance/{agent_type}", response_model=APIResponse)
async def get_agent_performance(
    agent_type: AgentType,
    days: int = 7,
    agent_service: AgentService = Depends()
):
    """获取 Agent 性能统计"""
    try:
        performance = await agent_service.get_performance_stats(agent_type, days)
        
        return APIResponse(
            success=True,
            data=performance
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/feedback", response_model=APIResponse)
async def submit_agent_feedback(
    feedback_data: Dict[str, Any],
    agent_service: AgentService = Depends()
):
    """提交 Agent 反馈"""
    try:
        result = await agent_service.submit_feedback(feedback_data)
        
        return APIResponse(
            success=True,
            message="反馈提交成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
