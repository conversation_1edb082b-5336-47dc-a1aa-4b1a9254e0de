"""
文件上传相关的 API 路由
"""
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import uuid
from datetime import datetime
import mimetypes

from models.schemas import (
    FileUploadResponse,
    APIResponse
)
from services.file_service import FileService
from config.settings import settings

router = APIRouter()


@router.post("/upload", response_model=APIResponse)
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    conversation_id: Optional[str] = Form(None),
    file_service: FileService = Depends()
):
    """上传文件"""
    try:
        # 检查文件大小
        if file.size and file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 ({settings.max_file_size} bytes)"
            )
        
        # 检查文件类型
        file_ext = os.path.splitext(file.filename or "")[1].lower()
        if file_ext not in settings.allowed_file_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        # 保存文件
        upload_result = await file_service.save_uploaded_file(
            file=file,
            description=description,
            conversation_id=conversation_id
        )
        
        return APIResponse(
            success=True,
            message="文件上传成功",
            data=upload_result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload/multiple", response_model=APIResponse)
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    description: Optional[str] = Form(None),
    conversation_id: Optional[str] = Form(None),
    file_service: FileService = Depends()
):
    """批量上传文件"""
    try:
        if len(files) > 10:  # 限制批量上传数量
            raise HTTPException(
                status_code=400,
                detail="批量上传文件数量不能超过10个"
            )
        
        upload_results = []
        failed_files = []
        
        for file in files:
            try:
                # 检查单个文件
                if file.size and file.size > settings.max_file_size:
                    failed_files.append({
                        "filename": file.filename,
                        "error": "文件大小超过限制"
                    })
                    continue
                
                file_ext = os.path.splitext(file.filename or "")[1].lower()
                if file_ext not in settings.allowed_file_types:
                    failed_files.append({
                        "filename": file.filename,
                        "error": f"不支持的文件类型: {file_ext}"
                    })
                    continue
                
                # 保存文件
                upload_result = await file_service.save_uploaded_file(
                    file=file,
                    description=description,
                    conversation_id=conversation_id
                )
                upload_results.append(upload_result)
                
            except Exception as e:
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })
        
        return APIResponse(
            success=True,
            message=f"批量上传完成，成功 {len(upload_results)} 个，失败 {len(failed_files)} 个",
            data={
                "successful_uploads": upload_results,
                "failed_uploads": failed_files
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{file_id}", response_model=APIResponse)
async def get_file_info(
    file_id: str,
    file_service: FileService = Depends()
):
    """获取文件信息"""
    try:
        file_info = await file_service.get_file_info(file_id)
        
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return APIResponse(
            success=True,
            data=file_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    file_service: FileService = Depends()
):
    """下载文件"""
    try:
        file_info = await file_service.get_file_info(file_id)
        
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        file_path = file_info["file_path"]
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在于服务器")
        
        # 获取 MIME 类型
        mime_type, _ = mimetypes.guess_type(file_path)
        
        return FileResponse(
            path=file_path,
            filename=file_info["filename"],
            media_type=mime_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{file_id}", response_model=APIResponse)
async def delete_file(
    file_id: str,
    file_service: FileService = Depends()
):
    """删除文件"""
    try:
        success = await file_service.delete_file(file_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return APIResponse(
            success=True,
            message="文件删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=APIResponse)
async def list_files(
    conversation_id: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    file_service: FileService = Depends()
):
    """获取文件列表"""
    try:
        files = await file_service.list_files(
            conversation_id=conversation_id,
            skip=skip,
            limit=limit
        )
        
        return APIResponse(
            success=True,
            data=files
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{file_id}/process", response_model=APIResponse)
async def process_file(
    file_id: str,
    processing_options: dict,
    file_service: FileService = Depends()
):
    """处理文件（预留接口）"""
    try:
        # 这里是文件处理的预留接口
        # 可以用于文档解析、OCR识别、数据提取等功能
        
        file_info = await file_service.get_file_info(file_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        processing_result = await file_service.process_file(
            file_id=file_id,
            options=processing_options
        )
        
        return APIResponse(
            success=True,
            message="文件处理完成",
            data=processing_result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{file_id}/preview", response_model=APIResponse)
async def preview_file(
    file_id: str,
    file_service: FileService = Depends()
):
    """预览文件内容（预留接口）"""
    try:
        file_info = await file_service.get_file_info(file_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        preview_data = await file_service.generate_preview(file_id)
        
        return APIResponse(
            success=True,
            data=preview_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/extract-data", response_model=APIResponse)
async def extract_data_from_file(
    file: UploadFile = File(...),
    extraction_type: str = Form("auto"),
    file_service: FileService = Depends()
):
    """从文件中提取数据（预留接口）"""
    try:
        # 这是一个预留接口，用于从上传的文件中提取财务数据
        # 可以支持发票、银行对账单、财务报表等文件的数据提取
        
        extracted_data = await file_service.extract_financial_data(
            file=file,
            extraction_type=extraction_type
        )
        
        return APIResponse(
            success=True,
            message="数据提取完成",
            data=extracted_data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
