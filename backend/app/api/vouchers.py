"""
记账凭证相关的 API 路由
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
import uuid
from datetime import datetime

from models.schemas import (
    VoucherCreate,
    VoucherResponse,
    VoucherStatus,
    APIResponse,
    AgentType
)
from services.voucher_service import VoucherService

router = APIRouter()


@router.post("/", response_model=APIResponse)
async def create_voucher(
    voucher: VoucherCreate,
    voucher_service: VoucherService = Depends()
):
    """创建记账凭证"""
    try:
        voucher_id = str(uuid.uuid4())
        
        new_voucher = await voucher_service.create_voucher(
            voucher_id=voucher_id,
            voucher_data=voucher
        )
        
        return APIResponse(
            success=True,
            message="凭证创建成功",
            data=new_voucher
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{voucher_id}", response_model=APIResponse)
async def get_voucher(
    voucher_id: str,
    voucher_service: VoucherService = Depends()
):
    """获取凭证详情"""
    try:
        voucher = await voucher_service.get_voucher(voucher_id)
        if not voucher:
            raise HTTPException(status_code=404, detail="凭证不存在")
        
        return APIResponse(
            success=True,
            data=voucher
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=APIResponse)
async def list_vouchers(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[VoucherStatus] = None,
    agent_type: Optional[AgentType] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    voucher_service: VoucherService = Depends()
):
    """获取凭证列表"""
    try:
        filters = {}
        if status:
            filters["status"] = status
        if agent_type:
            filters["created_by_agent"] = agent_type
        if date_from:
            filters["date_from"] = date_from
        if date_to:
            filters["date_to"] = date_to
        
        vouchers = await voucher_service.list_vouchers(
            skip=skip,
            limit=limit,
            filters=filters
        )
        
        return APIResponse(
            success=True,
            data=vouchers
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{voucher_id}", response_model=APIResponse)
async def update_voucher(
    voucher_id: str,
    voucher_update: VoucherCreate,
    voucher_service: VoucherService = Depends()
):
    """更新凭证"""
    try:
        updated_voucher = await voucher_service.update_voucher(
            voucher_id=voucher_id,
            voucher_data=voucher_update
        )
        
        if not updated_voucher:
            raise HTTPException(status_code=404, detail="凭证不存在")
        
        return APIResponse(
            success=True,
            message="凭证更新成功",
            data=updated_voucher
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{voucher_id}", response_model=APIResponse)
async def delete_voucher(
    voucher_id: str,
    voucher_service: VoucherService = Depends()
):
    """删除凭证"""
    try:
        success = await voucher_service.delete_voucher(voucher_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="凭证不存在")
        
        return APIResponse(
            success=True,
            message="凭证删除成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{voucher_id}/approve", response_model=APIResponse)
async def approve_voucher(
    voucher_id: str,
    approval_data: dict,
    voucher_service: VoucherService = Depends()
):
    """审批凭证"""
    try:
        result = await voucher_service.approve_voucher(
            voucher_id=voucher_id,
            approver_agent=approval_data.get("approver_agent"),
            comments=approval_data.get("comments")
        )
        
        return APIResponse(
            success=True,
            message="凭证审批成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{voucher_id}/reject", response_model=APIResponse)
async def reject_voucher(
    voucher_id: str,
    rejection_data: dict,
    voucher_service: VoucherService = Depends()
):
    """拒绝凭证"""
    try:
        result = await voucher_service.reject_voucher(
            voucher_id=voucher_id,
            reviewer_agent=rejection_data.get("reviewer_agent"),
            reason=rejection_data.get("reason")
        )
        
        return APIResponse(
            success=True,
            message="凭证已拒绝",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{voucher_id}/history", response_model=APIResponse)
async def get_voucher_history(
    voucher_id: str,
    voucher_service: VoucherService = Depends()
):
    """获取凭证历史记录"""
    try:
        history = await voucher_service.get_voucher_history(voucher_id)
        
        return APIResponse(
            success=True,
            data=history
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{voucher_id}/export", response_model=APIResponse)
async def export_voucher(
    voucher_id: str,
    export_format: str = "pdf",
    voucher_service: VoucherService = Depends()
):
    """导出凭证"""
    try:
        export_result = await voucher_service.export_voucher(
            voucher_id=voucher_id,
            format=export_format
        )
        
        return APIResponse(
            success=True,
            message="凭证导出成功",
            data=export_result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/summary", response_model=APIResponse)
async def get_voucher_statistics(
    days: int = Query(30, ge=1, le=365),
    voucher_service: VoucherService = Depends()
):
    """获取凭证统计信息"""
    try:
        stats = await voucher_service.get_statistics(days)
        
        return APIResponse(
            success=True,
            data=stats
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch/approve", response_model=APIResponse)
async def batch_approve_vouchers(
    voucher_ids: List[str],
    approval_data: dict,
    voucher_service: VoucherService = Depends()
):
    """批量审批凭证"""
    try:
        results = await voucher_service.batch_approve_vouchers(
            voucher_ids=voucher_ids,
            approver_agent=approval_data.get("approver_agent"),
            comments=approval_data.get("comments")
        )
        
        return APIResponse(
            success=True,
            message=f"批量审批完成，成功 {results['success_count']} 个",
            data=results
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
