"""
对话相关的 API 路由
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import List
import uuid
from datetime import datetime

from models.schemas import (
    ConversationCreate,
    ConversationResponse,
    MessageCreate,
    MessageResponse,
    APIResponse,
    WorkflowState
)
from services.conversation_service import ConversationService
from services.langgraph_service import LangGraphService

router = APIRouter()

# 依赖注入函数
def get_conversation_service():
    return ConversationService()

def get_langgraph_service():
    return LangGraphService()


@router.post("/", response_model=APIResponse)
async def create_conversation(
    conversation: ConversationCreate,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """创建新对话"""
    try:
        conversation_id = str(uuid.uuid4())
        
        # 创建对话记录
        new_conversation = await conversation_service.create_conversation(
            conversation_id=conversation_id,
            title=conversation.title,
            description=conversation.description
        )
        
        return APIResponse(
            success=True,
            message="对话创建成功",
            data={"conversation_id": conversation_id}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{conversation_id}", response_model=APIResponse)
async def get_conversation(
    conversation_id: str,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """获取对话详情"""
    try:
        conversation = await conversation_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        return APIResponse(
            success=True,
            data=conversation
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=APIResponse)
async def list_conversations(
    skip: int = 0,
    limit: int = 20,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """获取对话列表"""
    try:
        conversations = await conversation_service.list_conversations(
            skip=skip,
            limit=limit
        )
        
        return APIResponse(
            success=True,
            data=conversations
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{conversation_id}/messages", response_model=APIResponse)
async def send_message(
    conversation_id: str,
    message: MessageCreate,
    conversation_service: ConversationService = Depends(get_conversation_service),
    langgraph_service: LangGraphService = Depends(get_langgraph_service)
):
    """发送消息并触发 Agent 处理"""
    try:
        # 检查对话是否存在
        conversation = await conversation_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        # 保存用户消息
        user_message = await conversation_service.add_message(
            conversation_id=conversation_id,
            content=message.content,
            role=message.role,
            agent_type=message.agent_type,
            metadata=message.metadata
        )
        
        # 触发 LangGraph 工作流
        workflow_result = await langgraph_service.process_message(
            conversation_id=conversation_id,
            user_message=message.content,
            metadata=message.metadata
        )
        
        return APIResponse(
            success=True,
            message="消息处理成功",
            data={
                "user_message": user_message,
                "workflow_result": workflow_result
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{conversation_id}/messages", response_model=APIResponse)
async def get_messages(
    conversation_id: str,
    skip: int = 0,
    limit: int = 50,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """获取对话消息列表"""
    try:
        messages = await conversation_service.get_messages(
            conversation_id=conversation_id,
            skip=skip,
            limit=limit
        )
        
        return APIResponse(
            success=True,
            data=messages
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{conversation_id}/feedback", response_model=APIResponse)
async def submit_feedback(
    conversation_id: str,
    feedback: dict,
    langgraph_service: LangGraphService = Depends(get_langgraph_service)
):
    """提交人工反馈 (Human in the Loop)"""
    try:
        result = await langgraph_service.submit_human_feedback(
            conversation_id=conversation_id,
            feedback=feedback
        )
        
        return APIResponse(
            success=True,
            message="反馈提交成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{conversation_id}/state", response_model=APIResponse)
async def get_workflow_state(
    conversation_id: str,
    langgraph_service: LangGraphService = Depends(get_langgraph_service)
):
    """获取工作流状态"""
    try:
        state = await langgraph_service.get_workflow_state(conversation_id)
        
        return APIResponse(
            success=True,
            data=state
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
