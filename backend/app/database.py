"""
数据库配置和连接管理
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import redis.asyncio as redis
from typing import AsyncGenerator
import os

from config.settings import settings

# 创建数据库引擎
if settings.database_url.startswith("sqlite"):
    # SQLite 配置
    engine = create_engine(
        settings.database_url,
        connect_args={"check_same_thread": False}
    )
else:
    # PostgreSQL 配置
    engine = create_engine(settings.database_url)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# Redis 连接
redis_client = None


async def get_redis() -> redis.Redis:
    """获取 Redis 连接"""
    global redis_client
    if redis_client is None:
        redis_client = redis.from_url(settings.redis_url)
    return redis_client


async def get_db() -> AsyncGenerator[SessionLocal, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_db():
    """初始化数据库"""
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    # 创建必要的目录
    os.makedirs(settings.upload_dir, exist_ok=True)
    os.makedirs(settings.langgraph_checkpoint_dir, exist_ok=True)
    os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
    
    # 测试 Redis 连接
    try:
        redis_conn = await get_redis()
        await redis_conn.ping()
        print("Redis 连接成功")
    except Exception as e:
        print(f"Redis 连接失败: {e}")


async def close_db():
    """关闭数据库连接"""
    global redis_client
    if redis_client:
        await redis_client.close()
