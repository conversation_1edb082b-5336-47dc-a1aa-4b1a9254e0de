"""
会计工具类 - 提供会计相关的工具函数
"""
from typing import Dict, List, Any, Optional
import re
from decimal import Decimal, ROUND_HALF_UP


class AccountingTools:
    """会计工具类"""
    
    # 标准会计科目代码映射
    ACCOUNT_CODES = {
        # 资产类
        "1001": "库存现金",
        "1002": "银行存款",
        "1012": "其他货币资金",
        "1101": "短期投资",
        "1121": "应收票据",
        "1122": "应收账款",
        "1123": "预付账款",
        "1131": "应收股利",
        "1132": "应收利息",
        "1221": "其他应收款",
        "1401": "材料采购",
        "1402": "在途物资",
        "1403": "原材料",
        "1404": "材料成本差异",
        "1405": "库存商品",
        "1407": "商品进销差价",
        "1408": "委托加工物资",
        "1409": "委托代销商品",
        "1410": "受托代销商品",
        "1411": "存货跌价准备",
        "1501": "长期债券投资",
        "1502": "长期股权投资",
        "1601": "固定资产",
        "1602": "累计折旧",
        "1603": "固定资产减值准备",
        "1701": "无形资产",
        "1702": "累计摊销",
        
        # 负债类
        "2001": "短期借款",
        "2101": "应付票据",
        "2201": "应付账款",
        "2202": "预收账款",
        "2211": "应付职工薪酬",
        "2221": "应交税费",
        "2231": "应付利息",
        "2232": "应付股利",
        "2241": "其他应付款",
        "2501": "长期借款",
        "2701": "长期应付款",
        
        # 所有者权益类
        "3001": "实收资本",
        "3002": "资本公积",
        "3101": "盈余公积",
        "3103": "本年利润",
        "3104": "利润分配",
        
        # 成本类
        "4001": "生产成本",
        "4101": "制造费用",
        
        # 损益类
        "5001": "主营业务收入",
        "5051": "其他业务收入",
        "5101": "投资收益",
        "5201": "营业外收入",
        "5401": "主营业务成本",
        "5402": "其他业务成本",
        "5601": "销售费用",
        "5602": "管理费用",
        "5603": "财务费用",
        "5701": "营业外支出",
        "5801": "所得税费用",
    }
    
    @classmethod
    def get_account_name(cls, account_code: str) -> Optional[str]:
        """根据科目代码获取科目名称"""
        return cls.ACCOUNT_CODES.get(account_code)
    
    @classmethod
    def search_accounts(cls, keyword: str) -> List[Dict[str, str]]:
        """搜索会计科目"""
        results = []
        keyword_lower = keyword.lower()
        
        for code, name in cls.ACCOUNT_CODES.items():
            if (keyword_lower in name.lower() or 
                keyword_lower in code):
                results.append({
                    "code": code,
                    "name": name
                })
        
        return results
    
    @classmethod
    def validate_account_code(cls, account_code: str) -> bool:
        """验证科目代码是否有效"""
        return account_code in cls.ACCOUNT_CODES
    
    @classmethod
    def format_amount(cls, amount: float, precision: int = 2) -> str:
        """格式化金额"""
        if amount is None:
            return "0.00"
        
        # 使用 Decimal 进行精确计算
        decimal_amount = Decimal(str(amount))
        formatted = decimal_amount.quantize(
            Decimal('0.' + '0' * precision),
            rounding=ROUND_HALF_UP
        )
        
        return f"{formatted:,.{precision}f}"
    
    @classmethod
    def parse_amount(cls, amount_str: str) -> Optional[float]:
        """解析金额字符串"""
        if not amount_str:
            return None
        
        # 移除货币符号和空格
        cleaned = re.sub(r'[￥$¥,\s]', '', str(amount_str))
        
        try:
            return float(cleaned)
        except ValueError:
            return None
    
    @classmethod
    def validate_debit_credit_balance(cls, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证借贷平衡"""
        total_debit = Decimal('0')
        total_credit = Decimal('0')
        
        for entry in entries:
            debit = entry.get('debit_amount', 0) or 0
            credit = entry.get('credit_amount', 0) or 0
            
            total_debit += Decimal(str(debit))
            total_credit += Decimal(str(credit))
        
        difference = total_debit - total_credit
        is_balanced = abs(difference) < Decimal('0.01')  # 允许1分的误差
        
        return {
            'is_balanced': is_balanced,
            'total_debit': float(total_debit),
            'total_credit': float(total_credit),
            'difference': float(difference)
        }
    
    @classmethod
    def get_account_type(cls, account_code: str) -> Optional[str]:
        """获取科目类型"""
        if not account_code or len(account_code) < 1:
            return None
        
        first_digit = account_code[0]
        
        type_mapping = {
            '1': '资产',
            '2': '负债',
            '3': '所有者权益',
            '4': '成本',
            '5': '损益'
        }
        
        return type_mapping.get(first_digit)
    
    @classmethod
    def get_normal_balance_side(cls, account_code: str) -> Optional[str]:
        """获取科目的正常余额方向"""
        account_type = cls.get_account_type(account_code)
        
        if account_type in ['资产', '成本']:
            return '借方'
        elif account_type in ['负债', '所有者权益']:
            return '贷方'
        elif account_type == '损益':
            # 损益类需要进一步判断
            if account_code.startswith('5') and account_code < '5400':
                return '贷方'  # 收入类
            else:
                return '借方'  # 费用类
        
        return None
    
    @classmethod
    def suggest_contra_accounts(cls, account_code: str, amount: float) -> List[Dict[str, Any]]:
        """建议对应科目"""
        suggestions = []
        account_type = cls.get_account_type(account_code)
        
        # 这里是一个简化的建议逻辑，实际应用中可以更复杂
        if account_code == '1002':  # 银行存款
            suggestions.extend([
                {'code': '1122', 'name': '应收账款', 'reason': '收回应收账款'},
                {'code': '5001', 'name': '主营业务收入', 'reason': '销售收入'},
                {'code': '3001', 'name': '实收资本', 'reason': '投资款'}
            ])
        elif account_code == '1122':  # 应收账款
            suggestions.extend([
                {'code': '5001', 'name': '主营业务收入', 'reason': '赊销商品'},
                {'code': '1002', 'name': '银行存款', 'reason': '收回货款'}
            ])
        elif account_code == '5001':  # 主营业务收入
            suggestions.extend([
                {'code': '1002', 'name': '银行存款', 'reason': '现销收入'},
                {'code': '1122', 'name': '应收账款', 'reason': '赊销收入'}
            ])
        
        return suggestions
    
    @classmethod
    def generate_voucher_number(cls, date_str: str, sequence: int = 1) -> str:
        """生成凭证号"""
        # 格式: 记-YYYYMM-XXX
        from datetime import datetime
        
        try:
            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            year_month = date_obj.strftime('%Y%m')
            return f"记-{year_month}-{sequence:03d}"
        except:
            # 如果日期解析失败，使用当前日期
            now = datetime.now()
            year_month = now.strftime('%Y%m')
            return f"记-{year_month}-{sequence:03d}"
    
    @classmethod
    def validate_voucher_entries(cls, entries: List[Dict[str, Any]]) -> List[str]:
        """验证凭证分录"""
        errors = []
        
        if len(entries) < 2:
            errors.append("凭证至少需要两个分录")
        
        for i, entry in enumerate(entries):
            entry_num = i + 1
            
            # 检查必要字段
            if not entry.get('account_code'):
                errors.append(f"第{entry_num}个分录缺少科目代码")
            elif not cls.validate_account_code(entry['account_code']):
                errors.append(f"第{entry_num}个分录的科目代码无效")
            
            if not entry.get('account_name'):
                errors.append(f"第{entry_num}个分录缺少科目名称")
            
            # 检查金额
            debit = entry.get('debit_amount', 0) or 0
            credit = entry.get('credit_amount', 0) or 0
            
            if debit == 0 and credit == 0:
                errors.append(f"第{entry_num}个分录必须有借方或贷方金额")
            elif debit > 0 and credit > 0:
                errors.append(f"第{entry_num}个分录不能同时有借方和贷方金额")
            elif debit < 0 or credit < 0:
                errors.append(f"第{entry_num}个分录的金额不能为负数")
        
        # 检查借贷平衡
        balance_check = cls.validate_debit_credit_balance(entries)
        if not balance_check['is_balanced']:
            errors.append(f"借贷不平衡，差额: {balance_check['difference']}")
        
        return errors
