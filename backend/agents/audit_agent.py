"""
审计 Agent - 负责审查记账凭证的准确性和合规性
"""
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
import json

from config.settings import settings
from models.schemas import AgentType
from utils.accounting_tools import AccountingTools


class AuditAgent:
    """审计和合规检查 Agent"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.default_model,
            temperature=0.05,  # 更低的温度以确保一致性
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url
        )
        self.accounting_tools = AccountingTools()
        self.system_prompt = self._get_system_prompt()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """
你是一个专业的财务审计AI助手，负责审查记账凭证的准确性、合规性和完整性。

你的主要职责：
1. 检查记账凭证的会计处理是否正确
2. 验证借贷平衡和科目使用
3. 评估业务逻辑的合理性
4. 识别潜在的错误和风险
5. 提供改进建议

审计重点：
- 会计科目使用是否恰当
- 借贷方向是否正确
- 金额计算是否准确
- 凭证要素是否完整
- 是否符合会计准则
- 业务逻辑是否合理

审计标准：
- 严格按照企业会计准则
- 遵循谨慎性原则
- 确保信息的真实性和完整性
- 关注重要性和相关性
- 识别异常和风险点

你需要以客观、专业、严谨的态度进行审计，确保财务信息的质量。
"""
    
    async def review_voucher(
        self,
        voucher_data: Dict[str, Any],
        financial_analysis: str,
        conversation_id: str
    ) -> Dict[str, Any]:
        """审查记账凭证"""
        
        review_prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", """
请审查以下记账凭证：

财务分析：
{financial_analysis}

记账凭证：
{voucher_data}

请从以下维度进行全面审查：

1. 会计处理正确性
   - 科目选择是否恰当
   - 借贷方向是否正确
   - 金额是否准确

2. 合规性检查
   - 是否符合会计准则
   - 是否遵循相关法规
   - 凭证要素是否完整

3. 业务逻辑合理性
   - 业务描述是否清晰
   - 会计处理是否符合业务实质
   - 是否存在异常情况

4. 风险识别
   - 潜在的错误风险
   - 合规风险
   - 操作风险

请返回JSON格式的审查结果：
{{
    "approved": true/false,
    "confidence_score": 0-100,
    "feedback": "详细的审查意见",
    "issues": [
        {{
            "type": "error/warning/suggestion",
            "description": "问题描述",
            "suggestion": "改进建议"
        }}
    ],
    "requires_human_review": true/false,
    "risk_level": "low/medium/high"
}}
""")
        ])
        
        messages = review_prompt.format_messages(
            financial_analysis=financial_analysis,
            voucher_data=json.dumps(voucher_data, ensure_ascii=False, indent=2)
        )
        
        response = await self.llm.ainvoke(messages)
        
        try:
            review_result = json.loads(response.content)
            
            # 验证和补充审查结果
            validated_result = self._validate_review_result(review_result, voucher_data)
            
            return validated_result
            
        except json.JSONDecodeError:
            # 如果解析失败，返回默认的审查结果
            return {
                "approved": False,
                "confidence_score": 0,
                "feedback": "审查过程中出现错误，建议人工复核",
                "issues": [
                    {
                        "type": "error",
                        "description": "系统审查失败",
                        "suggestion": "需要人工审查"
                    }
                ],
                "requires_human_review": True,
                "risk_level": "high"
            }
    
    def _validate_review_result(self, review_result: Dict[str, Any], voucher_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证审查结果"""
        
        # 执行基础的数值检查
        basic_checks = self._perform_basic_checks(voucher_data)
        
        # 合并基础检查结果
        if basic_checks["issues"]:
            review_result.setdefault("issues", []).extend(basic_checks["issues"])
            if basic_checks["critical_errors"]:
                review_result["approved"] = False
                review_result["requires_human_review"] = True
                review_result["risk_level"] = "high"
        
        # 确保必要字段存在
        review_result.setdefault("approved", False)
        review_result.setdefault("confidence_score", 50)
        review_result.setdefault("feedback", "审查完成")
        review_result.setdefault("issues", [])
        review_result.setdefault("requires_human_review", False)
        review_result.setdefault("risk_level", "medium")
        
        # 根据问题数量调整置信度
        error_count = len([issue for issue in review_result["issues"] if issue["type"] == "error"])
        if error_count > 0:
            review_result["confidence_score"] = max(0, review_result["confidence_score"] - error_count * 20)
        
        return review_result
    
    def _perform_basic_checks(self, voucher_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行基础检查"""
        issues = []
        critical_errors = False
        
        # 检查借贷平衡
        total_debit = sum(
            entry.get("debit_amount", 0) or 0 
            for entry in voucher_data.get("entries", [])
        )
        total_credit = sum(
            entry.get("credit_amount", 0) or 0 
            for entry in voucher_data.get("entries", [])
        )
        
        if abs(total_debit - total_credit) > 0.01:
            issues.append({
                "type": "error",
                "description": f"借贷不平衡：借方 {total_debit}，贷方 {total_credit}",
                "suggestion": "检查并调整分录金额"
            })
            critical_errors = True
        
        # 检查分录数量
        entries = voucher_data.get("entries", [])
        if len(entries) < 2:
            issues.append({
                "type": "error",
                "description": "分录数量不足，至少需要两个分录",
                "suggestion": "添加必要的会计分录"
            })
            critical_errors = True
        
        # 检查科目代码格式
        for entry in entries:
            account_code = entry.get("account_code", "")
            if not account_code or not account_code.isdigit() or len(account_code) != 4:
                issues.append({
                    "type": "warning",
                    "description": f"科目代码格式异常：{account_code}",
                    "suggestion": "使用标准的4位数字科目代码"
                })
        
        # 检查金额合理性
        for entry in entries:
            debit = entry.get("debit_amount", 0) or 0
            credit = entry.get("credit_amount", 0) or 0
            
            if debit < 0 or credit < 0:
                issues.append({
                    "type": "error",
                    "description": f"金额不能为负数：{entry.get('account_name')}",
                    "suggestion": "检查金额的正负性"
                })
                critical_errors = True
            
            if debit > 0 and credit > 0:
                issues.append({
                    "type": "error",
                    "description": f"同一分录不能同时有借贷金额：{entry.get('account_name')}",
                    "suggestion": "每个分录只能有借方或贷方金额"
                })
                critical_errors = True
        
        return {
            "issues": issues,
            "critical_errors": critical_errors
        }
    
    async def provide_suggestions(
        self,
        voucher_data: Dict[str, Any],
        issues: List[Dict[str, Any]]
    ) -> str:
        """提供改进建议"""
        
        suggestion_prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", """
基于以下审查发现的问题，请提供详细的改进建议：

记账凭证：
{voucher_data}

发现的问题：
{issues}

请提供：
1. 具体的修改建议
2. 正确的会计处理方法
3. 相关的会计准则依据
4. 预防类似问题的措施

请以专业、建设性的语调提供建议。
""")
        ])
        
        messages = suggestion_prompt.format_messages(
            voucher_data=json.dumps(voucher_data, ensure_ascii=False, indent=2),
            issues=json.dumps(issues, ensure_ascii=False, indent=2)
        )
        
        response = await self.llm.ainvoke(messages)
        return response.content
    
    async def cross_check_with_financial_agent(
        self,
        voucher_data: Dict[str, Any],
        financial_reasoning: str
    ) -> Dict[str, Any]:
        """与财务Agent的分析进行交叉验证"""
        
        cross_check_prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", """
请对财务Agent的分析和生成的凭证进行交叉验证：

财务Agent的分析：
{financial_reasoning}

生成的凭证：
{voucher_data}

请验证：
1. 凭证是否准确反映了财务分析的结论
2. 会计处理是否与分析逻辑一致
3. 是否存在分析与凭证不匹配的情况
4. 财务分析是否遗漏了重要考虑因素

返回JSON格式的验证结果：
{{
    "consistency_score": 0-100,
    "analysis_quality": "excellent/good/fair/poor",
    "voucher_accuracy": "excellent/good/fair/poor", 
    "discrepancies": ["不一致之处列表"],
    "recommendations": ["改进建议列表"]
}}
""")
        ])
        
        messages = cross_check_prompt.format_messages(
            financial_reasoning=financial_reasoning,
            voucher_data=json.dumps(voucher_data, ensure_ascii=False, indent=2)
        )
        
        response = await self.llm.ainvoke(messages)
        
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            return {
                "consistency_score": 50,
                "analysis_quality": "fair",
                "voucher_accuracy": "fair",
                "discrepancies": ["无法完成交叉验证"],
                "recommendations": ["建议人工复核"]
            }
