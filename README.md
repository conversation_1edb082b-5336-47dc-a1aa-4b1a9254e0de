# 财务会计记账凭证自动生成 AI Agent 工具

基于 LangGraph、AG-UI 和 CopilotKit 构建的智能财务会计记账凭证生成系统。

## 功能特性

- **双 Agent 系统**: 财务 Agent 负责记账凭证生成，审计 Agent 负责审核
- **智能对话**: 支持两个 Agent 之间的智能对话和协作
- **长短期记忆**: Agent 具备上下文记忆能力
- **工具调用**: 支持多种财务工具和计算功能
- **人工审查**: Human in the Loop (HIL) 支持人工干预
- **多模型支持**: 支持多厂家大模型接口调用
- **文件上传**: 预留文件上传功能接口

## 技术栈

### 后端
- **LangChain**: AI 应用开发框架
- **LangGraph**: 工作流编排和状态管理
- **FastAPI**: 高性能 Web 框架

### 前端
- **AG-UI**: 现代化 UI 组件库
- **CopilotKit**: AI 助手集成框架
- **React**: 前端框架

## 项目结构

```
.
├── backend/                 # 后端服务
│   ├── app/                # FastAPI 应用
│   ├── agents/             # AI Agent 实现
│   ├── langgraph/          # LangGraph 工作流
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   └── requirements.txt    # Python 依赖
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   └── package.json       # Node.js 依赖
├── docs/                  # 项目文档
├── scripts/               # 脚本文件
└── startup.sh            # 项目启动脚本
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- Yarn

### 安装与运行

1. 克隆项目
```bash
git clone <repository-url>
cd account_3
```

2. 使用启动脚本
```bash
chmod +x startup.sh
./startup.sh
```

或者手动启动：

3. 启动后端
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. 启动前端
```bash
cd frontend
yarn install
yarn start
```

## API 文档

启动后端服务后，访问 http://localhost:8000/docs 查看 API 文档。

## 开发指南

详细的开发指南请参考 `docs/` 目录下的文档。

## 许可证

MIT License
