#!/bin/bash

# 财务会计记账凭证自动生成 AI Agent 工具启动脚本
# 作者: AI Assistant
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 创建虚拟环境
create_venv() {
    log_info "创建 Python 虚拟环境..."
    if [ ! -d "backend/venv" ]; then
        cd backend
        python3 -m venv venv
        cd ..
        log_success "虚拟环境创建成功"
    else
        log_info "虚拟环境已存在"
    fi
}

# 安装后端依赖
install_backend_deps() {
    log_info "安装后端依赖..."
    cd backend
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    cd ..
    log_success "后端依赖安装完成"
}

# 安装前端依赖
install_frontend_deps() {
    log_info "安装前端依赖..."
    cd frontend
    yarn install
    cd ..
    log_success "前端依赖安装完成"
}

# 创建环境配置文件
create_env_file() {
    if [ ! -f "backend/.env" ]; then
        log_info "创建环境配置文件..."
        cat > backend/.env << EOF
# 应用配置
APP_NAME=财务会计记账凭证AI Agent
DEBUG=true

# 数据库配置
DATABASE_URL=sqlite:///./accounting_agent.db
REDIS_URL=redis://localhost:6379/0

# AI 模型配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# 默认模型设置
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=gpt-4-turbo-preview

# 安全配置
SECRET_KEY=your-secret-key-change-in-production

# 日志配置
LOG_LEVEL=INFO
EOF
        log_success "环境配置文件创建完成"
        log_warning "请编辑 backend/.env 文件，配置你的 API 密钥"
    else
        log_info "环境配置文件已存在"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    cd backend
    source venv/bin/activate
    python -c "
from app.database import init_db
import asyncio
asyncio.run(init_db())
print('数据库初始化完成')
"
    cd ..
    log_success "数据库初始化完成"
}

# 启动 Redis（如果需要）
start_redis() {
    if ! pgrep -x "redis-server" > /dev/null; then
        log_info "启动 Redis 服务..."
        if command -v redis-server &> /dev/null; then
            redis-server --daemonize yes
            log_success "Redis 服务启动成功"
        else
            log_warning "Redis 未安装，将使用内存存储"
        fi
    else
        log_info "Redis 服务已运行"
    fi
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    cd backend
    source venv/bin/activate

    # 检查端口，如果8000被占用则使用8001
    BACKEND_PORT=8000
    if ! check_port 8000; then
        log_warning "端口 8000 被占用，尝试使用端口 8001"
        BACKEND_PORT=8001
        if ! check_port 8001; then
            log_error "端口 8000 和 8001 都被占用，请先停止占用这些端口的进程"
            exit 1
        fi
    fi

    # 启动 FastAPI 服务
    nohup uvicorn app.main:app --host 0.0.0.0 --port $BACKEND_PORT --reload > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    echo $BACKEND_PORT > ../backend.port

    cd ..

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    if curl -s http://localhost:$BACKEND_PORT/health > /dev/null; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        log_info "后端服务地址: http://localhost:$BACKEND_PORT"
        log_info "API 文档地址: http://localhost:$BACKEND_PORT/docs"
    else
        log_error "后端服务启动失败"
        exit 1
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    cd frontend
    
    # 检查端口
    if ! check_port 3000; then
        log_error "前端端口 3000 被占用，请先停止占用该端口的进程"
        exit 1
    fi
    
    # 启动 React 开发服务器
    nohup yarn start > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    
    cd ..
    
    # 等待服务启动
    sleep 5
    
    log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
    log_info "前端服务地址: http://localhost:3000"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止后端服务
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            log_success "后端服务已停止"
        fi
        rm -f backend.pid
    fi
    
    # 停止前端服务
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            log_success "前端服务已停止"
        fi
        rm -f frontend.pid
    fi
}

# 显示帮助信息
show_help() {
    echo "财务会计记账凭证自动生成 AI Agent 工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  setup     初始化项目环境"
    echo "  status    查看服务状态"
    echo "  logs      查看日志"
    echo "  help      显示帮助信息"
    echo ""
}

# 查看服务状态
show_status() {
    log_info "检查服务状态..."
    
    # 检查后端服务
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            log_success "后端服务运行中 (PID: $BACKEND_PID)"
        else
            log_error "后端服务未运行"
        fi
    else
        log_error "后端服务未启动"
    fi
    
    # 检查前端服务
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            log_success "前端服务运行中 (PID: $FRONTEND_PID)"
        else
            log_error "前端服务未运行"
        fi
    else
        log_error "前端服务未启动"
    fi
}

# 查看日志
show_logs() {
    echo "=== 后端日志 ==="
    if [ -f "logs/backend.log" ]; then
        tail -n 20 logs/backend.log
    else
        log_warning "后端日志文件不存在"
    fi
    
    echo ""
    echo "=== 前端日志 ==="
    if [ -f "logs/frontend.log" ]; then
        tail -n 20 logs/frontend.log
    else
        log_warning "前端日志文件不存在"
    fi
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p logs
    
    case "${1:-start}" in
        "setup")
            log_info "开始初始化项目环境..."
            check_command python3
            check_command yarn
            create_venv
            install_backend_deps
            install_frontend_deps
            create_env_file
            init_database
            log_success "项目环境初始化完成！"
            log_info "请编辑 backend/.env 文件配置 API 密钥，然后运行 './startup.sh start' 启动服务"
            ;;
        "start")
            log_info "启动财务会计 AI Agent 工具..."
            start_redis
            start_backend
            start_frontend
            log_success "所有服务启动完成！"
            log_info "前端地址: http://localhost:3000"
            log_info "后端地址: http://localhost:8000"
            log_info "API 文档: http://localhost:8000/docs"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            start_redis
            start_backend
            start_frontend
            log_success "服务重启完成！"
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获 Ctrl+C 信号
trap 'log_info "收到中断信号，正在停止服务..."; stop_services; exit 0' INT

# 运行主函数
main "$@"
