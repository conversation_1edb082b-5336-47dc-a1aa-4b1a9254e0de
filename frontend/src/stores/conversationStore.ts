/**
 * 对话状态管理
 */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Conversation, Message, ConversationStatus } from '../types';
import { conversationService } from '../services/conversationService';

interface ConversationState {
  // 状态
  conversations: Conversation[];
  currentConversation: Conversation | null;
  loading: boolean;
  error: string | null;

  // 操作
  setCurrentConversation: (conversation: Conversation | null) => void;
  setConversations: (conversations: Conversation[]) => void;
  addConversation: (conversation: Conversation) => void;
  updateConversation: (id: string, updates: Partial<Conversation>) => void;
  deleteConversation: (id: string) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 异步操作
  loadConversations: () => Promise<void>;
  createConversation: (title?: string, description?: string) => Promise<Conversation | null>;
  sendMessage: (conversationId: string, content: string, agentType?: string) => Promise<void>;
}

export const useConversationStore = create<ConversationState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      conversations: [],
      currentConversation: null,
      loading: false,
      error: null,

      // 同步操作
      setCurrentConversation: (conversation) => {
        set({ currentConversation: conversation });
      },

      setConversations: (conversations) => {
        set({ conversations });
      },

      addConversation: (conversation) => {
        set((state) => ({
          conversations: [conversation, ...state.conversations]
        }));
      },

      updateConversation: (id, updates) => {
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === id ? { ...conv, ...updates } : conv
          ),
          currentConversation: state.currentConversation?.id === id
            ? { ...state.currentConversation, ...updates }
            : state.currentConversation
        }));
      },

      deleteConversation: (id) => {
        set((state) => ({
          conversations: state.conversations.filter(conv => conv.id !== id),
          currentConversation: state.currentConversation?.id === id
            ? null
            : state.currentConversation
        }));
      },

      addMessage: (message) => {
        set((state) => {
          const updatedConversations = state.conversations.map(conv => {
            if (conv.id === message.conversation_id) {
              return {
                ...conv,
                messages: [...conv.messages, message],
                updated_at: new Date().toISOString()
              };
            }
            return conv;
          });

          const updatedCurrentConversation = state.currentConversation?.id === message.conversation_id
            ? {
                ...state.currentConversation,
                messages: [...state.currentConversation.messages, message],
                updated_at: new Date().toISOString()
              }
            : state.currentConversation;

          return {
            conversations: updatedConversations,
            currentConversation: updatedCurrentConversation
          };
        });
      },

      updateMessage: (messageId, updates) => {
        set((state) => {
          const updateMessages = (messages: Message[]) =>
            messages.map(msg => msg.id === messageId ? { ...msg, ...updates } : msg);

          const updatedConversations = state.conversations.map(conv => ({
            ...conv,
            messages: updateMessages(conv.messages)
          }));

          const updatedCurrentConversation = state.currentConversation
            ? {
                ...state.currentConversation,
                messages: updateMessages(state.currentConversation.messages)
              }
            : null;

          return {
            conversations: updatedConversations,
            currentConversation: updatedCurrentConversation
          };
        });
      },

      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      // 异步操作
      loadConversations: async () => {
        const { setLoading, setError, setConversations } = get();
        
        setLoading(true);
        setError(null);

        try {
          const response = await conversationService.getConversations();
          if (response.success && response.data) {
            setConversations(response.data);
          } else {
            setError(response.error || '加载对话列表失败');
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : '加载对话列表失败');
        } finally {
          setLoading(false);
        }
      },

      createConversation: async (title, description) => {
        const { setLoading, setError, addConversation } = get();
        
        setLoading(true);
        setError(null);

        try {
          const response = await conversationService.createConversation({
            title,
            description
          });

          if (response.success && response.data) {
            // 获取完整的对话信息
            const conversationResponse = await conversationService.getConversation(
              response.data.conversation_id
            );

            if (conversationResponse.success && conversationResponse.data) {
              const newConversation = conversationResponse.data;
              addConversation(newConversation);
              return newConversation;
            }
          } else {
            setError(response.error || '创建对话失败');
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : '创建对话失败');
        } finally {
          setLoading(false);
        }

        return null;
      },

      sendMessage: async (conversationId, content, agentType) => {
        const { setError, addMessage } = get();
        
        setError(null);

        try {
          const response = await conversationService.sendMessage(conversationId, {
            content,
            role: 'user',
            agent_type: agentType as any,
          });

          if (response.success && response.data) {
            // 添加用户消息
            if (response.data.user_message) {
              addMessage(response.data.user_message);
            }

            // 如果有 AI 响应，也添加到消息列表
            if (response.data.ai_response) {
              addMessage(response.data.ai_response);
            }
          } else {
            setError(response.error || '发送消息失败');
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : '发送消息失败');
        }
      }
    }),
    {
      name: 'conversation-store',
    }
  )
);
