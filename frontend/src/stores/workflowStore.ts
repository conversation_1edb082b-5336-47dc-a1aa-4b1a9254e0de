/**
 * 工作流状态管理
 */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { WorkflowState } from '../types';

interface WorkflowStoreState {
  workflowState: WorkflowState | null;
  setWorkflowState: (state: WorkflowState | null) => void;
  updateWorkflowState: (updates: Partial<WorkflowState>) => void;
  clearWorkflowState: () => void;
}

export const useWorkflowStore = create<WorkflowStoreState>()(
  devtools(
    (set) => ({
      workflowState: null,

      setWorkflowState: (state) => {
        set({ workflowState: state });
      },

      updateWorkflowState: (updates) => {
        set((state) => ({
          workflowState: state.workflowState
            ? { ...state.workflowState, ...updates }
            : null
        }));
      },

      clearWorkflowState: () => {
        set({ workflowState: null });
      }
    }),
    {
      name: 'workflow-store',
    }
  )
);
