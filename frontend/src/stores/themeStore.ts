/**
 * 主题状态管理
 */
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { ThemeConfig } from '../types';

interface ThemeState {
  themeConfig: ThemeConfig;
  setThemeConfig: (config: Partial<ThemeConfig>) => void;
  resetTheme: () => void;
}

const defaultThemeConfig: ThemeConfig = {
  primaryColor: '#1890ff',
  borderRadius: 6,
  colorBgContainer: '#ffffff',
};

export const useThemeStore = create<ThemeState>()(
  devtools(
    persist(
      (set) => ({
        themeConfig: defaultThemeConfig,

        setThemeConfig: (config) => {
          set((state) => ({
            themeConfig: { ...state.themeConfig, ...config }
          }));
        },

        resetTheme: () => {
          set({ themeConfig: defaultThemeConfig });
        }
      }),
      {
        name: 'theme-store',
      }
    ),
    {
      name: 'theme-store',
    }
  )
);
