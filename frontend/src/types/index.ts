/**
 * 前端类型定义
 */

export enum AgentType {
  FINANCIAL = 'financial',
  AUDIT = 'audit'
}

export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system'
}

export enum ConversationStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  ERROR = 'error'
}

export enum VoucherStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

// 基础接口
export interface BaseEntity {
  id: string;
  created_at?: string;
  updated_at?: string;
}

// 消息相关
export interface Message extends BaseEntity {
  content: string;
  role: MessageRole;
  agent_type?: AgentType;
  conversation_id: string;
  metadata: Record<string, any>;
}

export interface MessageCreate {
  content: string;
  role: MessageRole;
  agent_type?: AgentType;
  metadata?: Record<string, any>;
}

// 对话相关
export interface Conversation extends BaseEntity {
  title?: string;
  description?: string;
  status: ConversationStatus;
  messages: Message[];
}

export interface ConversationCreate {
  title?: string;
  description?: string;
}

// 会计分录
export interface AccountingEntry {
  account_code: string;
  account_name: string;
  debit_amount?: number;
  credit_amount?: number;
  description?: string;
}

// 记账凭证
export interface Voucher extends BaseEntity {
  voucher_date: string;
  voucher_number: string;
  description: string;
  status: VoucherStatus;
  entries: AccountingEntry[];
  attachments: string[];
  total_debit: number;
  total_credit: number;
  created_by_agent: AgentType;
  reviewed_by_agent?: AgentType;
}

export interface VoucherCreate {
  voucher_date: string;
  voucher_number?: string;
  description: string;
  entries: AccountingEntry[];
  attachments?: string[];
}

// Agent 配置
export interface AgentConfig {
  agent_type: AgentType;
  model_provider: string;
  model_name: string;
  temperature: number;
  max_tokens?: number;
  system_prompt?: string;
}

// Agent 响应
export interface AgentResponse {
  agent_type: AgentType;
  response: string;
  confidence?: number;
  reasoning?: string;
  suggested_actions?: string[];
}

// 工作流状态
export interface WorkflowState {
  conversation_id: string;
  current_step: string;
  financial_agent_response?: string;
  audit_agent_response?: string;
  voucher_draft?: VoucherCreate;
  human_feedback?: string;
  is_approved?: boolean;
  metadata: Record<string, any>;
}

// API 响应
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// 文件上传
export interface FileUploadResponse {
  filename: string;
  file_path: string;
  file_size: number;
  content_type: string;
  upload_time: string;
}

// 审计结果
export interface AuditIssue {
  type: 'error' | 'warning' | 'suggestion';
  description: string;
  suggestion: string;
}

export interface AuditResult {
  approved: boolean;
  confidence_score: number;
  feedback: string;
  issues: AuditIssue[];
  requires_human_review: boolean;
  risk_level: 'low' | 'medium' | 'high';
}

// UI 状态
export interface UIState {
  loading: boolean;
  error?: string;
  currentConversation?: Conversation;
  conversations: Conversation[];
  workflowState?: WorkflowState;
}

// CopilotKit 相关
export interface CopilotAction {
  name: string;
  description: string;
  parameters?: Record<string, any>;
  handler: (args: any) => Promise<any>;
}

// 表单数据
export interface VoucherFormData {
  voucher_date: string;
  description: string;
  entries: AccountingEntry[];
}

// 搜索和过滤
export interface SearchFilters {
  status?: VoucherStatus;
  agent_type?: AgentType;
  date_from?: string;
  date_to?: string;
  keyword?: string;
}

// 统计数据
export interface Statistics {
  total_conversations: number;
  total_vouchers: number;
  approved_vouchers: number;
  pending_vouchers: number;
  success_rate: number;
}

// 主题配置
export interface ThemeConfig {
  primaryColor: string;
  borderRadius: number;
  colorBgContainer: string;
}
