import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ConfigProvider, theme } from 'antd';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotSidebar } from '@copilotkit/react-ui';
import zhCN from 'antd/locale/zh_CN';

import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import ConversationPage from './pages/Conversation/ConversationPage';
import VoucherPage from './pages/Voucher/VoucherPage';
import SettingsPage from './pages/Settings/SettingsPage';
import { useThemeStore } from './stores/themeStore';

import './App.css';

// 创建 React Query 客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App: React.FC = () => {
  const { themeConfig } = useThemeStore();

  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: theme.defaultAlgorithm,
          token: {
            colorPrimary: themeConfig.primaryColor,
            borderRadius: themeConfig.borderRadius,
            colorBgContainer: themeConfig.colorBgContainer,
          },
        }}
      >
        <CopilotKit runtimeUrl="/api/copilot">
          <Router>
            <div className="App">
              <Layout>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/conversation/:id?" element={<ConversationPage />} />
                  <Route path="/vouchers" element={<VoucherPage />} />
                  <Route path="/settings" element={<SettingsPage />} />
                </Routes>
              </Layout>
              
              {/* CopilotKit 侧边栏 */}
              <CopilotSidebar
                instructions="你是一个专业的财务会计AI助手，帮助用户生成和审核记账凭证。"
                defaultOpen={false}
                labels={{
                  title: "财务AI助手",
                  initial: "你好！我是你的财务AI助手，可以帮你分析业务交易并生成记账凭证。请描述你需要处理的业务。",
                }}
              />
            </div>
          </Router>
        </CopilotKit>
      </ConfigProvider>
    </QueryClientProvider>
  );
};

export default App;
