{"name": "accounting-agent-frontend", "version": "1.0.0", "description": "财务会计记账凭证自动生成 AI Agent 前端", "private": true, "dependencies": {"@copilotkit/react-core": "^0.32.0", "@copilotkit/react-ui": "^0.32.0", "@copilotkit/react-textarea": "^0.32.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "@types/node": "^16.18.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "axios": "^1.6.0", "socket.io-client": "^4.7.0", "react-query": "^3.39.0", "zustand": "^4.4.0", "dayjs": "^1.11.0", "react-markdown": "^9.0.0", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.8.0", "framer-motion": "^10.16.0", "react-beautiful-dnd": "^13.1.1", "react-hook-form": "^7.48.0", "yup": "^1.4.0", "@hookform/resolvers": "^3.3.0"}, "devDependencies": {"@types/react-beautiful-dnd": "^13.1.0", "@types/react-syntax-highlighter": "^15.5.0", "eslint": "^8.0.0", "eslint-config-react-app": "^7.0.0", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}