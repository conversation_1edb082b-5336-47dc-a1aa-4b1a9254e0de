INFO:     Will watch for changes in these directories: ['/media/shun/bigdata/Projects/app365/account_3/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [92978] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/main.py", line 12, in <module>
    from app.api import router as api_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/__init__.py", line 5, in <module>
    from .conversations import router as conversations_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/conversations.py", line 18, in <module>
    from services.langgraph_service import LangGraphService
  File "/media/shun/bigdata/Projects/app365/account_3/backend/services/langgraph_service.py", line 8, in <module>
    from langgraph.workflow import AccountingWorkflow
  File "/media/shun/bigdata/Projects/app365/account_3/backend/langgraph/workflow.py", line 6, in <module>
    from langgraph import StateGraph, END
ImportError: cannot import name 'StateGraph' from 'langgraph' (/media/shun/bigdata/Projects/app365/account_3/backend/langgraph/__init__.py)
WARNING:  WatchFiles detected changes in 'services/langgraph_service.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/main.py", line 12, in <module>
    from app.api import router as api_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/__init__.py", line 5, in <module>
    from .conversations import router as conversations_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/conversations.py", line 18, in <module>
    from services.langgraph_service import LangGraphService
  File "/media/shun/bigdata/Projects/app365/account_3/backend/services/langgraph_service.py", line 8, in <module>
    from workflows.workflow import AccountingWorkflow
  File "/media/shun/bigdata/Projects/app365/account_3/backend/workflows/workflow.py", line 6, in <module>
    from langgraph import StateGraph, END
ImportError: cannot import name 'StateGraph' from 'langgraph' (unknown location)
WARNING:  WatchFiles detected changes in 'workflows/workflow.py'. Reloading...
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/main.py", line 12, in <module>
    from app.api import router as api_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/__init__.py", line 5, in <module>
    from .conversations import router as conversations_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/conversations.py", line 18, in <module>
    from services.langgraph_service import LangGraphService
  File "/media/shun/bigdata/Projects/app365/account_3/backend/services/langgraph_service.py", line 13, in <module>
    class LangGraphService:
  File "/media/shun/bigdata/Projects/app365/account_3/backend/services/langgraph_service.py", line 223, in LangGraphService
    async def get_workflow_history(self, conversation_id: str) -> List[Dict[str, Any]]:
NameError: name 'List' is not defined. Did you mean: 'list'?
WARNING:  WatchFiles detected changes in 'services/langgraph_service.py'. Reloading...
INFO:     Started server process [94095]
INFO:     Waiting for application startup.
2025-07-22 21:01:32.306 | INFO     | app.main:lifespan:20 - 启动财务会计AI Agent应用...
2025-07-22 21:01:32.308 | INFO     | app.main:lifespan:26 - 应用启动完成
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'services/langgraph_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-22 21:01:54.062 | INFO     | app.main:lifespan:31 - 关闭应用...
INFO:     Application shutdown complete.
INFO:     Finished server process [94095]
INFO:     Started server process [94107]
INFO:     Waiting for application startup.
2025-07-22 21:01:55.155 | INFO     | app.main:lifespan:20 - 启动财务会计AI Agent应用...
2025-07-22 21:01:55.156 | INFO     | app.main:lifespan:26 - 应用启动完成
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'workflows/workflow.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-22 21:02:14.109 | INFO     | app.main:lifespan:31 - 关闭应用...
INFO:     Application shutdown complete.
INFO:     Finished server process [94107]
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/usr/lib/python3.10/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/usr/lib/python3.10/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
  File "/media/shun/bigdata/Projects/app365/account_3/backend/venv/lib/python3.10/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/main.py", line 12, in <module>
    from app.api import router as api_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/__init__.py", line 5, in <module>
    from .conversations import router as conversations_router
  File "/media/shun/bigdata/Projects/app365/account_3/backend/app/api/conversations.py", line 18, in <module>
    from services.langgraph_service import LangGraphService
  File "/media/shun/bigdata/Projects/app365/account_3/backend/services/langgraph_service.py", line 7, in <module>
    from workflows.workflow import AccountingWorkflow
  File "/media/shun/bigdata/Projects/app365/account_3/backend/workflows/workflow.py", line 34, in <module>
    class AccountingWorkflow:
  File "/media/shun/bigdata/Projects/app365/account_3/backend/workflows/workflow.py", line 43, in AccountingWorkflow
    def _build_graph(self) -> StateGraph:
NameError: name 'StateGraph' is not defined
WARNING:  WatchFiles detected changes in 'workflows/workflow.py'. Reloading...
INFO:     Started server process [94176]
INFO:     Waiting for application startup.
2025-07-22 21:02:34.513 | INFO     | app.main:lifespan:20 - 启动财务会计AI Agent应用...
2025-07-22 21:02:34.516 | INFO     | app.main:lifespan:26 - 应用启动完成
INFO:     Application startup complete.
